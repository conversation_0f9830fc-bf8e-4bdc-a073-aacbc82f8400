import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_template_service.dart';

/// Provider for managing PDF settings across the app
class PdfSettingsProvider extends ChangeNotifier {
  // Default template type
  TemplateType _templateType = TemplateType.modern;

  // Default header and footer styles
  HeaderStyle _headerStyle = HeaderStyle.modern;
  FooterStyle _footerStyle = FooterStyle.modern;
  ContentStyle _contentStyle = ContentStyle.modern;

  // Default colors
  Color _primaryColor = const Color(0xFF2563EB); // Modern blue
  Color _accentColor = const Color(0xFF10B981);  // Modern green
  Color _textColor = const Color(0xFF1E293B);    // Slate 800

  // Default settings
  bool _showLogo = true;
  bool _showPageNumbers = true;
  bool _showTimestamp = true;
  bool _showWatermark = false;
  String _watermarkText = 'COPY';
  String _footerText = 'Tubewell Water Billing';
  String _companyName = 'Tubewell Water Billing';
  String? _companyAddress;
  String? _companyPhone;
  String? _companyEmail;
  String? _companyWebsite;

  // Getters
  TemplateType get templateType => _templateType;
  HeaderStyle get headerStyle => _headerStyle;
  FooterStyle get footerStyle => _footerStyle;
  ContentStyle get contentStyle => _contentStyle;
  Color get primaryColor => _primaryColor;
  Color get accentColor => _accentColor;
  Color get textColor => _textColor;
  bool get showLogo => _showLogo;
  bool get showPageNumbers => _showPageNumbers;
  bool get showTimestamp => _showTimestamp;
  bool get showWatermark => _showWatermark;
  String get watermarkText => _watermarkText;
  String get footerText => _footerText;
  String get companyName => _companyName;
  String? get companyAddress => _companyAddress;
  String? get companyPhone => _companyPhone;
  String? get companyEmail => _companyEmail;
  String? get companyWebsite => _companyWebsite;

  // Singleton instance
  static PdfSettingsProvider? _instance;

  // Factory constructor
  factory PdfSettingsProvider() {
    _instance ??= PdfSettingsProvider._internal();
    return _instance!;
  }

  // Private constructor
  PdfSettingsProvider._internal() {
    _loadSettings();
  }

  // Load settings from shared preferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // Load template settings
    _templateType = TemplateType.values[prefs.getInt('pdf_template_type') ?? 4]; // Default to modern
    _headerStyle = HeaderStyle.values[prefs.getInt('pdf_header_style') ?? 4]; // Default to modern
    _footerStyle = FooterStyle.values[prefs.getInt('pdf_footer_style') ?? 4]; // Default to modern
    _contentStyle = ContentStyle.values[prefs.getInt('pdf_content_style') ?? 2]; // Default to modern

    // Load colors
    _primaryColor = Color(prefs.getInt('pdf_primary_color') ?? 0xFF2563EB);
    _accentColor = Color(prefs.getInt('pdf_accent_color') ?? 0xFF10B981);
    _textColor = Color(prefs.getInt('pdf_text_color') ?? 0xFF1E293B);

    // Load other settings
    _showLogo = prefs.getBool('pdf_show_logo') ?? true;
    _showPageNumbers = prefs.getBool('pdf_show_page_numbers') ?? true;
    _showTimestamp = prefs.getBool('pdf_show_timestamp') ?? true;
    _showWatermark = prefs.getBool('pdf_show_watermark') ?? false;
    _watermarkText = prefs.getString('pdf_watermark_text') ?? 'COPY';
    _footerText = prefs.getString('pdf_footer_text') ?? 'Tubewell Water Billing';
    _companyName = prefs.getString('pdf_company_name') ?? 'Tubewell Water Billing';
    _companyAddress = prefs.getString('pdf_company_address');
    _companyPhone = prefs.getString('pdf_company_phone');
    _companyEmail = prefs.getString('pdf_company_email');
    _companyWebsite = prefs.getString('pdf_company_website');

    notifyListeners();
  }

  // Save settings to shared preferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // Save template settings
    prefs.setInt('pdf_template_type', _templateType.index);
    prefs.setInt('pdf_header_style', _headerStyle.index);
    prefs.setInt('pdf_footer_style', _footerStyle.index);
    prefs.setInt('pdf_content_style', _contentStyle.index);

    // Save colors using the non-deprecated way to access color values
    // Convert RGB components to a single integer value
    prefs.setInt('pdf_primary_color', (_primaryColor.r.toInt() << 16) | (_primaryColor.g.toInt() << 8) | _primaryColor.b.toInt());
    prefs.setInt('pdf_accent_color', (_accentColor.r.toInt() << 16) | (_accentColor.g.toInt() << 8) | _accentColor.b.toInt());
    prefs.setInt('pdf_text_color', (_textColor.r.toInt() << 16) | (_textColor.g.toInt() << 8) | _textColor.b.toInt());

    // Save other settings
    prefs.setBool('pdf_show_logo', _showLogo);
    prefs.setBool('pdf_show_page_numbers', _showPageNumbers);
    prefs.setBool('pdf_show_timestamp', _showTimestamp);
    prefs.setBool('pdf_show_watermark', _showWatermark);
    prefs.setString('pdf_watermark_text', _watermarkText);
    prefs.setString('pdf_footer_text', _footerText);
    prefs.setString('pdf_company_name', _companyName);

    if (_companyAddress != null) prefs.setString('pdf_company_address', _companyAddress!);
    if (_companyPhone != null) prefs.setString('pdf_company_phone', _companyPhone!);
    if (_companyEmail != null) prefs.setString('pdf_company_email', _companyEmail!);
    if (_companyWebsite != null) prefs.setString('pdf_company_website', _companyWebsite!);
  }

  // Set template type
  void setTemplateType(TemplateType type) {
    _templateType = type;

    // Update related styles based on template type
    switch (type) {
      case TemplateType.modern:
        _headerStyle = HeaderStyle.modern;
        _footerStyle = FooterStyle.modern;
        _contentStyle = ContentStyle.modern;
        break;
      case TemplateType.invoice:
        _headerStyle = HeaderStyle.detailed;
        _footerStyle = FooterStyle.detailed;
        _contentStyle = ContentStyle.cards;
        break;
      case TemplateType.report:
        _headerStyle = HeaderStyle.standard;
        _footerStyle = FooterStyle.standard;
        _contentStyle = ContentStyle.standard;
        break;
      case TemplateType.statement:
        _headerStyle = HeaderStyle.compact;
        _footerStyle = FooterStyle.compact;
        _contentStyle = ContentStyle.standard;
        break;
      default:
        _headerStyle = HeaderStyle.standard;
        _footerStyle = FooterStyle.standard;
        _contentStyle = ContentStyle.standard;
    }

    _saveSettings();
    notifyListeners();
  }

  // Set header style
  void setHeaderStyle(HeaderStyle style) {
    _headerStyle = style;
    _saveSettings();
    notifyListeners();
  }

  // Set footer style
  void setFooterStyle(FooterStyle style) {
    _footerStyle = style;
    _saveSettings();
    notifyListeners();
  }

  // Set content style
  void setContentStyle(ContentStyle style) {
    _contentStyle = style;
    _saveSettings();
    notifyListeners();
  }

  // Set primary color
  void setPrimaryColor(Color color) {
    _primaryColor = color;
    _saveSettings();
    notifyListeners();
  }

  // Set accent color
  void setAccentColor(Color color) {
    _accentColor = color;
    _saveSettings();
    notifyListeners();
  }

  // Set text color
  void setTextColor(Color color) {
    _textColor = color;
    _saveSettings();
    notifyListeners();
  }

  // Set show logo
  void setShowLogo(bool show) {
    _showLogo = show;
    _saveSettings();
    notifyListeners();
  }

  // Set show page numbers
  void setShowPageNumbers(bool show) {
    _showPageNumbers = show;
    _saveSettings();
    notifyListeners();
  }

  // Set show timestamp
  void setShowTimestamp(bool show) {
    _showTimestamp = show;
    _saveSettings();
    notifyListeners();
  }

  // Set show watermark
  void setShowWatermark(bool show) {
    _showWatermark = show;
    _saveSettings();
    notifyListeners();
  }

  // Set watermark text
  void setWatermarkText(String text) {
    _watermarkText = text;
    _saveSettings();
    notifyListeners();
  }

  // Set footer text
  void setFooterText(String text) {
    _footerText = text;
    _saveSettings();
    notifyListeners();
  }

  // Set company name
  void setCompanyName(String name) {
    _companyName = name;
    _saveSettings();
    notifyListeners();
  }

  // Set company address
  void setCompanyAddress(String? address) {
    _companyAddress = address;
    _saveSettings();
    notifyListeners();
  }

  // Set company phone
  void setCompanyPhone(String? phone) {
    _companyPhone = phone;
    _saveSettings();
    notifyListeners();
  }

  // Set company email
  void setCompanyEmail(String? email) {
    _companyEmail = email;
    _saveSettings();
    notifyListeners();
  }

  // Set company website
  void setCompanyWebsite(String? website) {
    _companyWebsite = website;
    _saveSettings();
    notifyListeners();
  }

  // Get current PDF settings
  PdfSettings getCurrentSettings() {
    return PdfSettings(
      templateType: _templateType,
      headerStyle: _headerStyle,
      footerStyle: _footerStyle,
      contentStyle: _contentStyle,
      primaryColor: PdfColor(
        _primaryColor.r / 255,
        _primaryColor.g / 255,
        _primaryColor.b / 255,
      ),
      accentColor: PdfColor(
        _accentColor.r / 255,
        _accentColor.g / 255,
        _accentColor.b / 255,
      ),
      textColor: PdfColor(
        _textColor.r / 255,
        _textColor.g / 255,
        _textColor.b / 255,
      ),
      showLogo: _showLogo,
      showPageNumbers: _showPageNumbers,
      showTimestamp: _showTimestamp,
      showWatermark: _showWatermark,
      watermarkText: _watermarkText,
      companyName: _companyName,
      companyAddress: _companyAddress,
      companyPhone: _companyPhone,
      companyEmail: _companyEmail,
      companyWebsite: _companyWebsite,
      additionalSettings: {
        'footerText': _footerText,
      },
    );
  }

  // Reset to default settings
  void resetToDefaults() {
    _templateType = TemplateType.modern;
    _headerStyle = HeaderStyle.modern;
    _footerStyle = FooterStyle.modern;
    _contentStyle = ContentStyle.modern;
    _primaryColor = const Color(0xFF2563EB);
    _accentColor = const Color(0xFF10B981);
    _textColor = const Color(0xFF1E293B);
    _showLogo = true;
    _showPageNumbers = true;
    _showTimestamp = true;
    _showWatermark = false;
    _watermarkText = 'COPY';
    _footerText = 'Tubewell Water Billing';
    _companyName = 'Tubewell Water Billing';
    _companyAddress = null;
    _companyPhone = null;
    _companyEmail = null;
    _companyWebsite = null;

    _saveSettings();
    notifyListeners();
  }

  // Apply a preset template
  void applyPreset(String presetName) {
    switch (presetName) {
      case 'modern':
        _templateType = TemplateType.modern;
        _headerStyle = HeaderStyle.modern;
        _footerStyle = FooterStyle.modern;
        _contentStyle = ContentStyle.modern;
        _primaryColor = const Color(0xFF2563EB); // Modern blue
        _accentColor = const Color(0xFF10B981); // Modern green
        break;
      case 'invoice':
        _templateType = TemplateType.invoice;
        _headerStyle = HeaderStyle.detailed;
        _footerStyle = FooterStyle.detailed;
        _contentStyle = ContentStyle.cards;
        _primaryColor = const Color(0xFF1976D2); // Blue
        _accentColor = const Color(0xFF2E7D32); // Green
        break;
      case 'report':
        _templateType = TemplateType.report;
        _headerStyle = HeaderStyle.standard;
        _footerStyle = FooterStyle.standard;
        _contentStyle = ContentStyle.standard;
        _primaryColor = const Color(0xFF2E7D32); // Green
        _accentColor = const Color(0xFF1976D2); // Blue
        break;
      case 'minimal':
        _templateType = TemplateType.standard;
        _headerStyle = HeaderStyle.minimal;
        _footerStyle = FooterStyle.minimal;
        _contentStyle = ContentStyle.standard;
        _primaryColor = const Color(0xFF263238); // Dark blue-grey
        _accentColor = const Color(0xFF78909C); // Light blue-grey
        _showLogo = false;
        break;
    }

    _saveSettings();
    notifyListeners();
  }
}
