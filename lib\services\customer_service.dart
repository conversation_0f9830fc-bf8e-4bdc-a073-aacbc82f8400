import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';

/// A service for handling customer operations, with enhanced deletion capabilities
/// and balance calculation functionality
class CustomerService {
  /// Safely delete a customer and all associated data
  ///
  /// This method will:
  /// 1. Delete all bills belonging to the customer
  /// 2. Delete all payments made by the customer
  /// 3. Delete any credit balance for the customer
  /// 4. Finally delete the customer record itself
  ///
  /// Returns a summary of what was deleted
  static Future<Map<String, dynamic>> deleteCustomerWithAllData(
      int customerId) async {
    // Starting deletion process

    // Fetch data counts before deletion for reporting
    try {
      // Fetch all related data for the customer
      final bills = await DatabaseService.getBillsByCustomer(customerId);
      final payments = await DatabaseService.getPaymentsByCustomer(customerId);
      final credit = await DatabaseService.getCustomerCredit(customerId);
      final customer = await DatabaseService.getCustomerById(customerId);

      if (customer == null) {
        // Customer not found, abort deletion
        return {
          'success': false,
          'error': 'Customer not found',
        };
      }

      final customerName = customer.name;
      final billCount = bills.length;
      final paymentCount = payments.length;
      final creditAmount = credit?.amount ?? 0;

      try {
        // Perform deletion
        final success = await DatabaseService.deleteCustomer(customerId);

        if (success) {
          // Successfully deleted customer
          return {
            'success': true,
            'customerName': customerName,
            'deletedItems': {
              'bills': billCount,
              'payments': paymentCount,
              'credit': creditAmount > 0
                  ? CurrencyService.formatCurrency(creditAmount,
                      decimalPlaces: 2)
                  : 'None',
            }
          };
        } else {
          // Database reported deletion failed
          return {
            'success': false,
            'error':
                'Failed to delete customer - database operation unsuccessful',
          };
        }
      } catch (e) {
        // Error during customer deletion
        return {
          'success': false,
          'error': 'Error during deletion: $e',
        };
      }
    } catch (e) {
      // Error fetching customer data before deletion
      return {
        'success': false,
        'error': 'Error preparing for deletion: $e',
      };
    }
  }

  /// Get all customers with their statistics
  static Future<List<Map<String, dynamic>>> getCustomersWithStats() async {
    final customers = await DatabaseService.getAllCustomers();
    final result = <Map<String, dynamic>>[];

    for (final customer in customers) {
      final bills = await DatabaseService.getBillsByCustomer(customer.id);
      final payments = await DatabaseService.getPaymentsByCustomer(customer.id);
      final credit = await DatabaseService.getCustomerCredit(customer.id);

      // Calculate statistics
      final totalBills = bills.length;
      final unpaidBills = bills.where((bill) => !bill.isPaid).length;
      final totalPayments = payments.length;
      final creditAmount = credit?.amount ?? 0;

      // Sum up bill amounts
      double totalBilledAmount = 0;
      for (final bill in bills) {
        totalBilledAmount += bill.amount;
      }

      // Sum up payment amounts
      double totalPaidAmount = 0;
      for (final payment in payments) {
        totalPaidAmount += payment.amount;
      }

      result.add({
        'customer': customer,
        'stats': {
          'totalBills': totalBills,
          'unpaidBills': unpaidBills,
          'totalPayments': totalPayments,
          'totalBilledAmount': totalBilledAmount,
          'totalPaidAmount': totalPaidAmount,
          'creditAmount': creditAmount,
        }
      });
    }

    return result;
  }

  /// Calculate the outstanding balance for a customer
  /// Returns a negative value if the customer has outstanding bills
  /// Returns a positive value if the customer has credit
  static Future<double> getCustomerBalance(int customerId) async {
    // Get all bills for the customer
    final bills = await DatabaseService.getBillsByCustomer(customerId);

    // Get customer credit
    final credit = await DatabaseService.getCustomerCredit(customerId);

    // Calculate total outstanding amount from bills
    double outstandingAmount = 0;
    for (final bill in bills) {
      if (!bill.isPaid) {
        outstandingAmount += bill.outstandingAmount;
      }
    }

    // Calculate net balance (credit - outstanding)
    double creditAmount = credit?.amount ?? 0;
    double netBalance = creditAmount - outstandingAmount;

    return netBalance;
  }

  /// Get detailed balance information for a customer
  static Future<Map<String, dynamic>> getCustomerBalanceDetails(
      int customerId) async {
    // Get all bills for the customer
    final bills = await DatabaseService.getBillsByCustomer(customerId);

    // Get customer credit
    final credit = await DatabaseService.getCustomerCredit(customerId);

    // Calculate total outstanding amount from bills
    double outstandingAmount = 0;
    for (final bill in bills) {
      if (!bill.isPaid) {
        outstandingAmount += bill.outstandingAmount;
      }
    }

    // Calculate net balance (credit - outstanding)
    double creditAmount = credit?.amount ?? 0;
    double netBalance = creditAmount - outstandingAmount;

    return {
      'outstandingAmount': outstandingAmount,
      'creditAmount': creditAmount,
      'netBalance': netBalance,
      'hasOutstanding': outstandingAmount > 0,
      'hasCredit': creditAmount > 0,
    };
  }

  /// Get balance information for multiple customers
  static Future<Map<int, double>> getBalanceForCustomers(
      List<Customer> customers) async {
    final Map<int, double> balances = {};

    for (final customer in customers) {
      final balance = await getCustomerBalance(customer.id);
      balances[customer.id] = balance;
    }

    return balances;
  }

  /// Get summary of all customers including total credit and total dues
  static Future<Map<String, dynamic>> getCustomersSummary() async {
    try {
      // Get all customers
      final customers = await DatabaseService.getAllCustomers();
      int totalCount = customers.length;
      int activeCount = 0;
      double totalCredit = 0.0;
      double totalDue = 0.0;

      // Get all customer credits
      final creditsList = await DatabaseService.getAllCustomerCredits();
      final creditsMap = {
        for (var credit in creditsList) credit.customerId: credit.amount
      };

      // Get outstanding bills for all customers
      for (final customer in customers) {
        final bills = await DatabaseService.getBillsByCustomer(customer.id);

        // Calculate outstanding amount
        double outstandingAmount = 0;
        for (final bill in bills) {
          if (!bill.isPaid) {
            outstandingAmount += bill.outstandingAmount;
          }
        }

        // Get credit amount for this customer
        double creditAmount = creditsMap[customer.id] ?? 0;

        // Calculate net balance
        double netBalance = creditAmount - outstandingAmount;

        // Update totals
        if (netBalance > 0) {
          totalCredit += netBalance;
          activeCount++;
        } else if (netBalance < 0) {
          totalDue += netBalance.abs();
          activeCount++;
        }
      }

      return {
        'totalCount': totalCount,
        'activeCount': activeCount,
        'totalCredit': totalCredit,
        'totalDue': totalDue,
      };
    } catch (e) {
      return {
        'totalCount': 0,
        'activeCount': 0,
        'totalCredit': 0.0,
        'totalDue': 0.0,
      };
    }
  }

  /// Get a customer by phone number
  static Future<Customer?> getCustomerByPhone(String phoneNumber) async {
    try {
      final customers = await DatabaseService.getAllCustomers();
      final formattedSearchNumber = _formatPhoneNumber(phoneNumber);

      // Find customer with matching phone number
      for (final customer in customers) {
        if (customer.contactNumber != null) {
          final customerNumber = _formatPhoneNumber(customer.contactNumber!);

          // Try exact match first
          if (customerNumber == formattedSearchNumber) {
            return customer;
          }

          // If no exact match, try matching the last 10 digits (ignoring country code)
          final searchLastDigits = formattedSearchNumber.length > 10
              ? formattedSearchNumber.substring(formattedSearchNumber.length - 10)
              : formattedSearchNumber;

          final customerLastDigits = customerNumber.length > 10
              ? customerNumber.substring(customerNumber.length - 10)
              : customerNumber;

          if (searchLastDigits.length >= 10 &&
              customerLastDigits.length >= 10 &&
              searchLastDigits == customerLastDigits) {
            return customer;
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Format a phone number to contain only digits
  static String _formatPhoneNumber(String phone) {
    // Remove all non-digit characters
    return phone.replaceAll(RegExp(r'\D'), '');
  }

  /// Create a new customer
  static Future<Customer?> createCustomer({
    required String name,
    String? contactNumber,
    String? address,
  }) async {
    try {
      // Standardize phone number if provided
      String? standardizedNumber;
      if (contactNumber != null && contactNumber.isNotEmpty) {
        standardizedNumber = _formatPhoneNumber(contactNumber);
        // Add country code if needed
        if (standardizedNumber.length <= 10) {
          final countryCode = CurrencyService.getCountryCode();
          standardizedNumber = '$countryCode$standardizedNumber';
        }
      }

      final customer = Customer(
        id: 0, // Will be set by the database
        name: name,
        contactNumber: standardizedNumber,
        createdAt: DateTime.now(), // This will be handled by the Customer constructor
      );

      // Use the saveCustomer method which is available in DatabaseService
      final customerId = await DatabaseService.saveCustomer(customer);
      if (customerId > 0) {
        return Customer(
          id: customerId,
          name: name,
          contactNumber: standardizedNumber,
          createdAt: customer.createdAt, // This is already a DateTime object
        );
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}
