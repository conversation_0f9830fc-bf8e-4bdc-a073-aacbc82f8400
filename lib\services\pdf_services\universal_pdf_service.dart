import 'dart:io';
import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing_app/models/bill.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/models/expense.dart';
import 'package:tubewell_water_billing_app/models/payment.dart';
import 'package:tubewell_water_billing_app/providers/pdf_settings_provider.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_bill_details.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_customer_detail.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_customer_list.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_expenses.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_payments.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_summary.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_transaction.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_utility_service.dart';

/// A universal service for handling PDF generation across the app
class UniversalPdfService {
  /// Handle PDF generation based on the current screen and data
  static Future<void> handlePdf(
    BuildContext context,
    Map<String, dynamic>? pdfData, {
    bool autoOpen = true,
    bool showSaveOption = false,
    bool showShareOption = false,
    bool showPrintOption = false,
    PdfSettings? pdfSettings,
  }) async {
    // Get global PDF settings if not provided
    final globalSettings =
        Provider.of<PdfSettingsProvider>(context, listen: false);
    final defaultSettings = globalSettings.getCurrentSettings();

    // Use provided settings or global settings
    pdfSettings ??= defaultSettings;
    // Store context references to avoid async gap issues
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    if (pdfData == null) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('No data available for PDF generation'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Show loading indicator
      _showLoading(context);

      // Determine which PDF to generate based on the data
      File? pdfFile;

      // Check for customer list
      if (pdfData.containsKey('customers')) {
                final List<Customer> customers = pdfData['customers'] as List<Customer>;
        pdfFile = await PdfCustomerListService.generateCustomerListPdf(
          customers: customers,
          summary: pdfData['summary'] as Map<String, dynamic>?,
        );
      }
      // Check for bill details
      else if (pdfData.containsKey('bill') && pdfData.containsKey('customer')) {
                final Bill bill = pdfData['bill'] as Bill;
        final Customer customer = pdfData['customer'] as Customer;
        final List<Payment> payments = pdfData['payments'] as List<Payment>? ?? <Payment>[];

        // Create an appropriate settings object
        // Since pdfSettings is already initialized with a default value if null
        var effectiveSettings = pdfSettings;

        // Create a map for additional settings
        Map<String, dynamic> newAdditionalSettings =
            Map<String, dynamic>.from(effectiveSettings.additionalSettings ?? {});

        // Add any additional settings passed from the bill details screen
        if (pdfData.containsKey('title')) {
          newAdditionalSettings['title'] = pdfData['title'];
        }

        if (pdfData.containsKey('reference')) {
          newAdditionalSettings['reference'] = pdfData['reference'];
        }

        // Create a new settings object with the updated additionalSettings
        effectiveSettings = effectiveSettings.copyWith(
            additionalSettings: newAdditionalSettings);


        // Generate the bill PDF
        pdfFile = await PdfBillDetailsService.generateBillPdf(
          bill: bill,
          customer: customer,
          payments: payments,
          settings: effectiveSettings,
        );

              }
      // Check for payments
      else if (pdfData.containsKey('payments')) {
                final List<Payment> payments = pdfData['payments'] as List<Payment>;
        final Customer? customer = pdfData['customer'] as Customer?;
        final bool useModernTable = pdfData.containsKey('useModernTable')
            ? pdfData['useModernTable'] as bool
            : false;

        pdfFile = await PdfPaymentsService.generatePaymentsPdf(
          customer: customer,
          payments: payments,
          settings: pdfSettings,
          useModernTable: useModernTable,
        );
      }
      // Check for expenses
      else if (pdfData.containsKey('expenses')) {
                final List<Expense> expenses = pdfData['expenses'] as List<Expense>;
        pdfFile = await PdfExpensesService.generateExpensesPdf(
          expenses: expenses,
          summary: pdfData['summary'] as Map<String, double>?,
          categoryTotals: pdfData['categoryTotals'] as Map<String, double>?,
          filters: pdfData['filters'] as Map<String, dynamic>?,
        );
      }
      // Check for transactions
      else if (pdfData.containsKey('transactions')) {
                final List<Payment> transactions =
            pdfData['transactions'] as List<Payment>;
        // Use the transaction service instead since we removed the transactions service
        pdfFile = await PdfTransactionService.generateTransactionsListPdf(
          bills: [], // Empty bills list as we're handling payments
          customersMap: {}, // Empty customers map
          summary: {
            'totalAmount':
                transactions.fold(0.0, (sum, payment) => sum + payment.amount),
            'count': transactions.length,
          },
          filters: pdfData['filters'] as Map<String, dynamic>?,
        );
      }
      // Check for transactions list (bills)
      else if (pdfData.containsKey('bills') &&
          pdfData.containsKey('customersMap')) {
                final List<Bill> bills = pdfData['bills'] as List<Bill>;
        final Map<int, Customer> customersMap =
            pdfData['customersMap'] as Map<int, Customer>;
        pdfFile = await PdfTransactionService.generateTransactionsListPdf(
          bills: bills,
          customersMap: customersMap,
          summary: pdfData['summary'] as Map<String, num>?,
          filters: pdfData['filters'] as Map<String, dynamic>?,
        );
      }
      // Check for summary
      else if (pdfData.containsKey('summary') &&
          pdfData.containsKey('accountSummary')) {
                // Always use the enhanced summary service since we removed the basic one
        pdfFile = await PdfSummaryService.generateSummaryReportPdf(
          accountSummary: pdfData['accountSummary'] as Map<String, double>,
          billSummary: pdfData['billSummary'] as Map<String, num>,
          dateRange: pdfData['dateRange'] as Map<String, dynamic>?,
          activeTab: pdfData.containsKey('activeTab')
              ? (pdfData['activeTab'] as int)
              : 0,
          recentBills: pdfData['recentBills'] as List<Bill>?,
          recentPayments: pdfData['recentPayments'] as List<Payment>?,
          recentExpenses: pdfData['recentExpenses'] as List<Expense>?,
          expenseCategories: pdfData['expenseCategories'] as Map<String, double>?,
          paymentMethods: pdfData['paymentMethods'] as Map<String, double>?,
          customersMap: pdfData['customersMap'] as Map<int, Customer>?,
        );
      }
      // Check for customer detail
      else if (pdfData.containsKey('customerDetail')) {
                final Customer customer = pdfData['customer'] as Customer;
        final List<Bill>? bills = pdfData['bills'] as List<Bill>?;

        // Create a local variable to hold the settings
        // Since pdfSettings is already initialized with a default value if null
        final PdfSettings settings = pdfSettings;

        pdfFile =
            await PdfCustomerDetailService.generateCustomerTransactionsPdf(
          customer: customer,
          bills: bills,
          settings: settings,
        );
      }
      // No matching PDF type
      else {
        // Hide loading indicator
        navigator.pop();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content:
                Text('Unknown PDF type requested: ${pdfData.keys.join(", ")}'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Hide loading indicator
      navigator.pop();

      // Handle PDF options or auto-open
      final shouldShowOptionsDialog =
          showSaveOption || showShareOption || showPrintOption;

      // We don't need to check for null here as pdfFile is guaranteed to be non-null
      // by the code structure above

      if (!pdfFile.existsSync()) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('PDF file not found at path: ${pdfFile.path}'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }


      // Store the file reference
      final generatedPdfFile = pdfFile;

      // Create a separate function to handle showing options dialog
      void handlePdfOptions() {
        _showPdfOptionsDialog(
          navigator.context,
          generatedPdfFile,
          autoOpen: autoOpen,
          showSaveOption: showSaveOption,
          showShareOption: showShareOption,
          showPrintOption: showPrintOption,
        );
      }

      if (shouldShowOptionsDialog) {
        // Call the function directly to avoid BuildContext across async gaps
        handlePdfOptions();
      } else if (autoOpen) {
        // Open the generated PDF - pass the already captured scaffoldMessenger
        await _openPdf(generatedPdfFile, scaffoldMessenger);
      }
    } catch (e, stackTrace) {

      // Hide loading indicator if it's showing
      try {
        navigator.pop();
      } catch (_) {
        // Ignore if no dialog to pop
      }

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'DETAILS',
            onPressed: () {
              showDialog(
                context: navigator.context,
                builder: (context) => AlertDialog(
                  title: const Text('Error Details'),
                  content: SingleChildScrollView(
                    child: Text('$e\n\n$stackTrace'),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('CLOSE'),
                    ),
                  ],
                ),
              );
            },
            textColor: Colors.white,
          ),
        ),
      );
    }
  }

  /// Show a loading dialog
  static void _showLoading(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Generating PDF...'),
          ],
        ),
      ),
    );
  }

  /// Open the generated PDF file
  static Future<void> _openPdf(
      File pdfFile, ScaffoldMessengerState scaffoldMessenger) async {
    try {
      // First attempt to open directly
      await PdfUtilityService.openPdf(pdfFile);
    } catch (e) {

      // If direct opening fails, show options to the user
      if (scaffoldMessenger.mounted) {
        showDialog(
          context: scaffoldMessenger.context,
          builder: (context) => AlertDialog(
            title: const Text('PDF Generated'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'The PDF has been created but could not be opened automatically.',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text('What would you like to do with the PDF?'),
                const SizedBox(height: 8),
                Text(
                  'File location: ${pdfFile.path}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  PdfUtilityService.sharePdf(pdfFile, scaffoldMessenger);
                },
                child: const Text('SHARE'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Call our local method which handles the scaffoldMessenger
                  savePdfToDownloads(pdfFile, scaffoldMessenger);
                },
                child: const Text('SAVE'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('CANCEL'),
              ),
            ],
          ),
        );
      } else {
        // If context is not available, fall back to a simple error message
        _showSnackBarMessage(
          scaffoldMessenger,
          'PDF created but could not be opened. Try the share or save options.',
          isError: true,
        );
      }
    }
  }

  /// Save PDF to downloads folder
  static Future<void> savePdfToDownloads(
      File pdfFile, ScaffoldMessengerState scaffoldMessenger) async {
    try {
      // Request storage permission first
      final hasPermission = await PdfUtilityService.requestStoragePermission();
      if (!hasPermission) {
        _showSnackBarMessage(
          scaffoldMessenger,
          'Storage permission denied. Cannot save PDF.',
        );
        return;
      }

      // PdfUtilityService.savePdfToDownloads only expects a single file parameter
      final savedPath = await PdfUtilityService.savePdfToDownloads(pdfFile);
      _showSnackBarMessage(
        scaffoldMessenger,
        'PDF saved to: $savedPath',
        isError: false,
      );
    } catch (e) {
      _showSnackBarMessage(
        scaffoldMessenger,
        'Error saving PDF: $e',
      );
    }
  }

  /// Share PDF file
  static Future<void> sharePdf(
      File pdfFile, ScaffoldMessengerState scaffoldMessenger) async {
    try {
      await PdfUtilityService.sharePdf(pdfFile, scaffoldMessenger);
    } catch (e) {
      // Error is already handled by the utility service
    }
  }

  /// Print PDF file
  static Future<void> printPdf(
      File pdfFile, ScaffoldMessengerState scaffoldMessenger) async {
    try {
      final bytes = await pdfFile.readAsBytes();
      await Printing.layoutPdf(onLayout: (_) => bytes);

      _showSnackBarMessage(
        scaffoldMessenger,
        'PDF sent to printer',
        isError: false,
      );
    } catch (e) {
      _showSnackBarMessage(
        scaffoldMessenger,
        'Error printing PDF: $e',
      );
    }
  }

  /// Helper method to show a snackbar message without using BuildContext
  static void _showSnackBarMessage(
      ScaffoldMessengerState scaffoldMessenger, String message,
      {bool isError = true}) {
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  /// Show a dialog with PDF options
  static void _showPdfOptionsDialog(
    BuildContext context,
    File pdfFile, {
    bool autoOpen = true,
    bool showSaveOption = true,
    bool showShareOption = true,
    bool showPrintOption = true,
  }) {
    // Store context references to avoid async gap issues
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('PDF Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (autoOpen)
              ListTile(
                leading: const Icon(Icons.open_in_new, color: Colors.blue),
                title: const Text('Open PDF'),
                onTap: () {
                  Navigator.of(dialogContext).pop();
                  _openPdf(pdfFile, scaffoldMessenger);
                },
              ),
            if (showSaveOption)
              ListTile(
                leading: const Icon(Icons.save, color: Colors.green),
                title: const Text('Save to Downloads'),
                onTap: () {
                  Navigator.of(dialogContext).pop();
                  savePdfToDownloads(pdfFile, scaffoldMessenger);
                },
              ),
            if (showShareOption)
              ListTile(
                leading: const Icon(Icons.share, color: Colors.orange),
                title: const Text('Share PDF'),
                onTap: () {
                  Navigator.of(dialogContext).pop();
                  sharePdf(pdfFile, scaffoldMessenger);
                },
              ),
            if (showPrintOption)
              ListTile(
                leading: const Icon(Icons.print, color: Colors.purple),
                title: const Text('Print PDF'),
                onTap: () {
                  Navigator.of(dialogContext).pop();
                  printPdf(pdfFile, scaffoldMessenger);
                },
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

