import 'dart:io';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing_app/models/bill.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/models/expense.dart';
import 'package:tubewell_water_billing_app/models/payment.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_utility_service.dart';

/// Service for generating comprehensive summary report PDFs
class PdfSummaryService {
  /// Generate a comprehensive summary report PDF
  static Future<File> generateSummaryReportPdf({
    required Map<String, double> accountSummary,
    required Map<String, num> billSummary,
    Map<String, dynamic>? dateRange,
    int activeTab = 0,
    List<Bill>? recentBills,
    List<Payment>? recentPayments,
    List<Expense>? recentExpenses,
    Map<String, double>? expenseCategories,
    Map<String, double>? paymentMethods,
    Map<int, Customer>? customersMap,
  }) async {
    // If customersMap is not provided, create an empty one
    final Map<int, Customer> customerData = customersMap ?? {};
    try {
      // Load fonts
      await PdfBaseService.loadFonts();
      final regularFont = PdfBaseService.regularFont;
      final boldFont = PdfBaseService.boldFont;
      final italicFont = PdfBaseService.italicFont;

      // Define colors
      final PdfColor primaryColor = PdfBaseService.primaryColor; // Blue
      final PdfColor accentColor = PdfBaseService.accentColor; // Green
      final PdfColor expenseColor = PdfColor.fromHex('#C62828'); // Red
      final PdfColor incomeColor = PdfColor.fromHex('#1976D2'); // Blue
      final PdfColor lightGreen = PdfBaseService.lightGreen;
      final PdfColor lightBlue = PdfBaseService.lightBlue;
      final PdfColor lightRed = PdfBaseService.lightRed;

      // Create a PDF document
      final pdf = PdfBaseService.createDocument();

      // Add page
      pdf.addPage(pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header with logo and company info
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('TUBEWELL WATER BILLING',
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 24,
                          color: primaryColor,
                        )),
                    pw.SizedBox(height: 5),
                    pw.Text('Summary Report',
                        style: pw.TextStyle(
                          font: italicFont,
                          fontSize: 12,
                          color: accentColor,
                        )),
                  ],
                ),
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text(
                        'Business Overview',
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 16,
                        )),
                    if (dateRange != null && (dateRange['startDate'] != null || dateRange['endDate'] != null))
                      pw.Text(
                          'Period: ${dateRange['startDate'] != null ? DateFormat('dd/MM/yyyy').format(dateRange['startDate']) : 'Start'} - ${dateRange['endDate'] != null ? DateFormat('dd/MM/yyyy').format(dateRange['endDate']) : 'End'}',
                          style: pw.TextStyle(font: regularFont, fontSize: 10)),
                  ],
                ),
              ],
            ),
            pw.SizedBox(height: 20),

            // Financial Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: lightGreen,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('FINANCIAL SUMMARY',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 14,
                        color: primaryColor,
                      )),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Income:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(accountSummary['income'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: boldFont,
                                    fontSize: 16,
                                    color: incomeColor)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Expenses:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(accountSummary['expenses'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: boldFont,
                                    fontSize: 16,
                                    color: expenseColor)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Net Balance:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(accountSummary['netBalance'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: boldFont,
                                    fontSize: 16,
                                    color: (accountSummary['netBalance'] ?? 0) >= 0 ? primaryColor : expenseColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // Bills Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: lightBlue,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('BILLS SUMMARY',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 14,
                        color: accentColor,
                      )),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Bills:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('${billSummary['totalBills'] ?? 0}',
                                style: pw.TextStyle(font: boldFont, fontSize: 14)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Amount:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(billSummary['totalAmount'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(font: boldFont, fontSize: 14)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Paid Bills:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('${billSummary['paidBills'] ?? 0}',
                                style: pw.TextStyle(font: regularFont, fontSize: 12)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Partially Paid:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('${billSummary['partialBills'] ?? 0}',
                                style: pw.TextStyle(font: regularFont, fontSize: 12)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Unpaid Bills:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('${billSummary['unpaidBills'] ?? 0}',
                                style: pw.TextStyle(font: regularFont, fontSize: 12)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Paid Amount:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(billSummary['paidAmount'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: regularFont,
                                    fontSize: 12,
                                    color: primaryColor)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Outstanding:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(billSummary['outstandingAmount'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: regularFont,
                                    fontSize: 12,
                                    color: expenseColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // Recent Transactions
            if (recentBills != null && recentBills.isNotEmpty) ...[
              pw.Text('RECENT BILLS',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: primaryColor,
                  )),
              pw.Divider(color: primaryColor),
              pw.Table(
                border: pw.TableBorder.all(
                  color: PdfColor.fromHex('#CCCCCC'),
                  width: 0.5,
                ),
                columnWidths: {
                  0: const pw.FixedColumnWidth(30), // ID
                  1: const pw.FixedColumnWidth(70), // Date
                  2: const pw.FlexColumnWidth(2), // Customer
                  3: const pw.FixedColumnWidth(60), // Amount
                  4: const pw.FixedColumnWidth(50), // Status
                },
                children: [
                  // Table header
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      color: lightGreen,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('ID',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Date',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Customer',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Amount',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Status',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                    ],
                  ),
                  // Table rows - limit to 5 most recent
                  ...recentBills.take(5).map((bill) {
                    // Determine status color
                    final PdfColor statusColor = bill.isPaid
                        ? primaryColor
                        : (bill.isPartiallyPaid ? accentColor : PdfColor.fromHex('#C62828'));

                    // Determine status text
                    final String billStatus = bill.isPaid
                        ? 'PAID'
                        : (bill.isPartiallyPaid ? 'PARTIAL' : 'UNPAID');

                    return pw.TableRow(
                      decoration: pw.BoxDecoration(
                        color: bill.isPaid
                            ? PdfBaseService.colorWithOpacity(lightGreen, 0.2)
                            : (bill.isPartiallyPaid
                                ? PdfBaseService.colorWithOpacity(lightBlue, 0.2)
                                : PdfBaseService.colorWithOpacity(lightRed, 0.2)),
                      ),
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text('#${bill.id}',
                              style: pw.TextStyle(font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              DateFormat('dd/MM/yyyy').format(bill.billDate),
                              style: pw.TextStyle(font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              customerData[bill.customerId]?.name ?? 'Customer #${bill.customerId}',
                              style: pw.TextStyle(font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              'Rs. ${bill.amount.toStringAsFixed(2)}',
                              style: pw.TextStyle(font: boldFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(billStatus,
                              style: pw.TextStyle(
                                  font: boldFont,
                                  fontSize: 9,
                                  color: statusColor)),
                        ),
                      ],
                    );
                  }),
                ],
              ),
              pw.SizedBox(height: 20),
            ],

            // Footer
            pw.SizedBox(height: 30),
            pw.Divider(color: PdfColor.fromHex('#CCCCCC')),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                    'Generated: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                    style: pw.TextStyle(font: italicFont, fontSize: 9)),
                pw.Text('Tubewell Water Billing System',
                    style: pw.TextStyle(font: italicFont, fontSize: 9)),
              ],
            ),
          ];
        },
      ));

      // Save the PDF file
      final fileName = 'summary_report_${DateFormat('yyyyMMdd').format(DateTime.now())}.pdf';
      return await PdfUtilityService.savePdfToTemp(pdf, fileName);
    } catch (e) {
      rethrow;
    }
  }
}