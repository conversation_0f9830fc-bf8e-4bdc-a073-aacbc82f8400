import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing_app/models/bill.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/models/payment.dart';
import 'package:tubewell_water_billing_app/screens/bill_details_screen.dart';
import 'package:tubewell_water_billing_app/screens/customer_detail_screen.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';
import 'package:tubewell_water_billing_app/widgets/info_chips.dart';

typedef OnPaymentActionCallback = void Function(Payment payment);

/// A reusable widget for displaying bill entries in a list
class BillListItem extends StatelessWidget {
  final Bill bill;
  final Customer? customer;
  final VoidCallback? onDelete;
  final VoidCallback? onEdit;
  final VoidCallback? onMarkPaid;
  final VoidCallback? onRefresh;
  final bool showMoreOptions;

  const BillListItem({
    super.key,
    required this.bill,
    required this.customer,
    this.onDelete,
    this.onEdit,
    this.onMarkPaid,
    this.onRefresh,
    this.showMoreOptions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => BillDetailsScreen(
                bill: bill,
                customer: customer,
              ),
            ),
          ).then((_) {
            if (onRefresh != null) onRefresh!();
          });
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Customer chip
                  Expanded(
                    child: GestureDetector(
                      onTap: customer == null
                          ? null
                          : () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => CustomerDetailScreen(
                                    customer: customer!,
                                  ),
                                ),
                              ).then((_) {
                                if (onRefresh != null) onRefresh!();
                              });
                            },
                      child: StyledInfoChip(
                        icon: Icons.person,
                        text: customer?.name ?? 'Unknown Customer',
                        colorScheme: ChipColorScheme.green,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),

                  // Status chip
                  _buildStatusChip(),

                  if (showMoreOptions) ...[
                    const SizedBox(width: 8),
                    _buildOptionsButton(context),
                  ] else if (onDelete != null) ...[
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: onDelete,
                      child: const StyledInfoChip(
                        icon: Icons.delete,
                        text: '',
                        colorScheme: ChipColorScheme.red,
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),
              const Divider(height: 1),
              const SizedBox(height: 12),

              // Bill details rows
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date row
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 16,
                            color: Colors.grey.shade700,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            formatDate(bill.billDate),
                            style: TextStyle(
                              color: Colors.grey.shade800,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Duration row
                      Row(
                        children: [
                          Icon(
                            Icons.timer,
                            size: 16,
                            color: Colors.grey.shade700,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            formatDuration(
                                bill.durationHoursWhole, bill.durationMinutes),
                            style: TextStyle(
                              color: Colors.grey.shade800,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // Amount
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Bill #${bill.id}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        CurrencyService.formatCurrency(bill.amount),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                      if (bill.isPartiallyPaid == true &&
                          bill.partialAmount != null)
                        Text(
                          CurrencyService.formatCurrency(bill.partialAmount!),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    ChipColorScheme colorScheme;
    String text;
    IconData icon;

    if (bill.isPaid) {
      text = 'PAID';
      colorScheme = ChipColorScheme.green;
      icon = Icons.check_circle;
    } else if (bill.isPartiallyPaid == true) {
      text = 'PARTIAL';
      colorScheme = ChipColorScheme.blue;
      icon = Icons.pending;
    } else {
      text = 'UNPAID';
      colorScheme = ChipColorScheme.red;
      icon = Icons.unpublished;
    }

    return StyledInfoChip(
      icon: icon,
      text: text,
      colorScheme: colorScheme,
    );
  }

  Widget _buildOptionsButton(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Icon(Icons.more_vert, color: Colors.grey.shade700, size: 16),
      ),
      onSelected: (String option) {
        switch (option) {
          case 'delete':
            if (onDelete != null) onDelete!();
            break;
          case 'edit':
            if (onEdit != null) onEdit!();
            break;
          case 'mark_paid':
            if (onMarkPaid != null) onMarkPaid!();
            break;
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        if (onEdit != null)
          const PopupMenuItem<String>(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit, size: 20),
              title: Text('Edit Bill'),
              contentPadding: EdgeInsets.zero,
              dense: true,
            ),
          ),
        if (!bill.isPaid && onMarkPaid != null)
          const PopupMenuItem<String>(
            value: 'mark_paid',
            child: ListTile(
              leading: Icon(Icons.check_circle, size: 20),
              title: Text('Mark as Paid'),
              contentPadding: EdgeInsets.zero,
              dense: true,
            ),
          ),
        if (onDelete != null)
          const PopupMenuItem<String>(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: Colors.red, size: 20),
              title: Text('Delete Bill', style: TextStyle(color: Colors.red)),
              contentPadding: EdgeInsets.zero,
              dense: true,
            ),
          ),
      ],
    );
  }

  // Helper functions
  String formatDate(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }

  String formatDuration(int hours, int minutes) {
    if (hours == 0) {
      return '$minutes minutes';
    } else if (minutes == 0) {
      return '$hours hours';
    } else {
      return '$hours hours $minutes minutes';
    }
  }
}

/// A reusable widget for displaying payment entries in a list
class PaymentListItemWidget extends StatelessWidget {
  final Payment payment;
  final OnPaymentActionCallback? onTap;
  final OnPaymentActionCallback? onEdit;
  final OnPaymentActionCallback? onDelete;
  final bool showMoreOptions;
  final Color? backgroundColor;

  const PaymentListItemWidget({
    super.key,
    required this.payment,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showMoreOptions = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      color: backgroundColor ?? Colors.blue.shade50,
      child: InkWell(
        onTap: onTap != null ? () => onTap!(payment) : null,
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.payment, color: Colors.blue),
              title: Text(
                CurrencyService.formatCurrency(payment.amount),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: Color(0xFF2E7D32),
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date: ${DateFormat('dd MMM yyyy').format(payment.paymentDate)}',
                  ),
                  if (payment.paymentMethod != null)
                    Text('Method: ${payment.paymentMethod}'),
                  if (payment.remarks != null && payment.remarks!.isNotEmpty)
                    Text(
                      'Remarks: ${payment.remarks}',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
              isThreeLine:
                  payment.remarks != null && payment.remarks!.isNotEmpty,
              trailing: showMoreOptions
                  ? PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert),
                      onSelected: (value) {
                        if (value == 'edit' && onEdit != null) {
                          onEdit!(payment);
                        } else if (value == 'delete' && onDelete != null) {
                          onDelete!(payment);
                        }
                      },
                      itemBuilder: (context) => [
                        if (onEdit != null)
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 18),
                                SizedBox(width: 8),
                                Text('Edit Payment'),
                              ],
                            ),
                          ),
                        if (onDelete != null)
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 18, color: Colors.red),
                                SizedBox(width: 8),
                                Text('Delete Payment',
                                    style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                      ],
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }
}
