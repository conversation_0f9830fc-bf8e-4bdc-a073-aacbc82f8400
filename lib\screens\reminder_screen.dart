import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing_app/models/bill.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';
import 'package:tubewell_water_billing_app/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing_app/widgets/app_drawer.dart';
import 'package:tubewell_water_billing_app/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing_app/widgets/reminder_dialog.dart';
import 'package:tubewell_water_billing_app/screens/bill_details_screen.dart';

class ReminderScreen extends StatefulWidget {
  const ReminderScreen({super.key});

  @override
  State<ReminderScreen> createState() => _ReminderScreenState();
}

class _ReminderScreenState extends State<ReminderScreen>
    with AutomaticKeepAliveClientMixin {

  bool _isLoading = true;
  List<Bill> _unpaidBills = [];
  Map<int, Customer> _customersMap = {};
  Map<int, List<Bill>> _customerBillsMap = {};

  // For filtering
  String _searchQuery = '';
  bool _showPartiallyPaid = true;
  bool _showFullyUnpaid = true;

  // For sorting
  String _sortBy = 'date'; // 'date', 'amount', 'customer'
  bool _sortAscending = false;

  // For data change subscription
  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  // For search debouncing
  Timer? _searchDebounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _loadData();

    // Subscribe to data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.bill ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.customer ||
          changeType == DataChangeType.all) {
        _loadData();
      }
    });
  }

  // Debounced search to reduce unnecessary rebuilds
  void _onSearchChanged(String value) {
    if (_searchDebounceTimer?.isActive ?? false) _searchDebounceTimer?.cancel();

    _searchDebounceTimer = Timer(_debounceDuration, () {
      if (mounted) {
        setState(() {
          _searchQuery = value;
        });
      }
    });
  }

  @override
  void dispose() {
    _dataChangeSubscription.cancel();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load all unpaid and partially paid bills
      final bills = await DatabaseService.getAllBills();
      final unpaidBills = bills.where((bill) => !bill.isPaid).toList();

      // Load all customers
      final customers = await DatabaseService.getAllCustomers();
      final customersMap = {for (var c in customers) c.id: c};

      // Group bills by customer
      final customerBillsMap = <int, List<Bill>>{};
      for (var bill in unpaidBills) {
        if (!customerBillsMap.containsKey(bill.customerId)) {
          customerBillsMap[bill.customerId] = [];
        }
        customerBillsMap[bill.customerId]!.add(bill);
      }

      // Apply sorting to each customer's bills
      customerBillsMap.forEach((customerId, bills) {
        _sortBills(bills);
      });

      if (mounted) {
        setState(() {
          _unpaidBills = unpaidBills;
          _customersMap = customersMap;
          _customerBillsMap = customerBillsMap;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  void _sortBills(List<Bill> bills) {
    switch (_sortBy) {
      case 'date':
        bills.sort((a, b) => _sortAscending
            ? a.billDate.compareTo(b.billDate)
            : b.billDate.compareTo(a.billDate));
        break;
      case 'amount':
        bills.sort((a, b) => _sortAscending
            ? a.outstandingAmount.compareTo(b.outstandingAmount)
            : b.outstandingAmount.compareTo(a.outstandingAmount));
        break;
      default:
        bills.sort((a, b) => _sortAscending
            ? a.billDate.compareTo(b.billDate)
            : b.billDate.compareTo(a.billDate));
    }
  }

  void _sortCustomers(List<MapEntry<int, List<Bill>>> entries) {
    switch (_sortBy) {
      case 'customer':
        entries.sort((a, b) {
          final customerA = _customersMap[a.key]?.name ?? '';
          final customerB = _customersMap[b.key]?.name ?? '';
          return _sortAscending
              ? customerA.compareTo(customerB)
              : customerB.compareTo(customerA);
        });
        break;
      case 'amount':
        entries.sort((a, b) {
          final totalA = a.value
              .fold<double>(0, (sum, bill) => sum + bill.outstandingAmount);
          final totalB = b.value
              .fold<double>(0, (sum, bill) => sum + bill.outstandingAmount);
          return _sortAscending
              ? totalA.compareTo(totalB)
              : totalB.compareTo(totalA);
        });
        break;
      case 'date':
        entries.sort((a, b) {
          // Sort by most recent bill date
          final latestBillA = a.value.reduce((curr, next) =>
              curr.billDate.isAfter(next.billDate) ? curr : next);
          final latestBillB = b.value.reduce((curr, next) =>
              curr.billDate.isAfter(next.billDate) ? curr : next);
          return _sortAscending
              ? latestBillA.billDate.compareTo(latestBillB.billDate)
              : latestBillB.billDate.compareTo(latestBillA.billDate);
        });
        break;
      default:
        // Default sort by customer name
        entries.sort((a, b) {
          final customerA = _customersMap[a.key]?.name ?? '';
          final customerB = _customersMap[b.key]?.name ?? '';
          return customerA.compareTo(customerB);
        });
    }
  }

  List<MapEntry<int, List<Bill>>> _getFilteredAndSortedCustomerBills() {
    // Filter bills based on search query and payment status
    final filteredMap = <int, List<Bill>>{};

    _customerBillsMap.forEach((customerId, bills) {
      final customer = _customersMap[customerId];
      if (customer == null) return;

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!customer.name.toLowerCase().contains(query)) {
          return;
        }
      }

      // Filter by payment status
      final filteredBills = bills.where((bill) {
        if (bill.isPartiallyPaid && !_showPartiallyPaid) return false;
        if (!bill.isPartiallyPaid && !_showFullyUnpaid) return false;
        return true;
      }).toList();

      if (filteredBills.isNotEmpty) {
        filteredMap[customerId] = filteredBills;
      }
    });

    // Convert to list for sorting
    final entries = filteredMap.entries.toList();

    // Sort customers
    _sortCustomers(entries);

    return entries;
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text('Show Partially Paid Bills'),
              value: _showPartiallyPaid,
              onChanged: (value) {
                setState(() {
                  _showPartiallyPaid = value ?? true;
                  Navigator.pop(context);
                });
              },
            ),
            CheckboxListTile(
              title: const Text('Show Fully Unpaid Bills'),
              value: _showFullyUnpaid,
              onChanged: (value) {
                setState(() {
                  _showFullyUnpaid = value ?? true;
                  Navigator.pop(context);
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CLOSE'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort By'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Date'),
              value: 'date',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                  Navigator.pop(context);
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Amount'),
              value: 'amount',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                  Navigator.pop(context);
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Customer Name'),
              value: 'customer',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                  Navigator.pop(context);
                });
              },
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('Ascending Order'),
              value: _sortAscending,
              onChanged: (value) {
                setState(() {
                  _sortAscending = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('APPLY'),
          ),
        ],
      ),
    );
  }

  void _showReminderDialog(Customer customer, List<Bill> bills) {
    showDialog(
      context: context,
      builder: (context) => ReminderDialog(
        customer: customer,
        bills: bills,
        title: 'Send Payment Reminder',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Payment Reminders',
        showBackButton: true,
      ),
      drawer: const AppDrawer(currentScreen: 'reminders'),
      body: _buildContent(),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_unpaidBills.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.notifications_none,
        title: 'No Unpaid Bills',
        message: 'There are no unpaid bills to send reminders for.',
      );
    }

    final filteredEntries = _getFilteredAndSortedCustomerBills();

    if (filteredEntries.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.filter_alt,
        title: 'No Matching Bills',
        message: 'No bills match your current filter settings.',
      );
    }

    return Column(
      children: [
        // Search and filter bar
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Search customers...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: _onSearchChanged,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: _showFilterDialog,
                tooltip: 'Filter',
              ),
              IconButton(
                icon: const Icon(Icons.sort),
                onPressed: _showSortDialog,
                tooltip: 'Sort',
              ),
            ],
          ),
        ),

        // Customer list with unpaid bills
        Expanded(
          child: ListView.builder(
            itemCount: filteredEntries.length,
            itemBuilder: (context, index) {
              final entry = filteredEntries[index];
              final customerId = entry.key;
              final bills = entry.value;
              final customer = _customersMap[customerId]!;

              // Calculate total outstanding amount
              final totalOutstanding = bills.fold<double>(
                  0, (sum, bill) => sum + bill.outstandingAmount);

              return _buildCustomerCard(customer, bills, totalOutstanding);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerCard(
      Customer customer, List<Bill> bills, double totalOutstanding) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ExpansionTile(
        title: Text(
          customer.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                '${bills.length} unpaid ${bills.length == 1 ? 'bill' : 'bills'}'),
            Text(
              'Total: ${CurrencyService.formatCurrency(totalOutstanding)}',
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade700,
          child: Text(
            customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '?',
            style: const TextStyle(color: Colors.white),
          ),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.send),
          onPressed: () => _showReminderDialog(customer, bills),
          tooltip: 'Send Reminder',
        ),
        children: [
          ...bills.map((bill) => _buildBillItem(bill, customer)),
        ],
      ),
    );
  }

  Widget _buildBillItem(Bill bill, Customer customer) {
    final dateFormat = DateFormat('dd MMM yyyy');

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      title: Text('Bill #${bill.id}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Date: ${dateFormat.format(bill.billDate)}'),
          Text(
            bill.isPartiallyPaid
                ? 'Partially Paid: ${CurrencyService.formatCurrency(bill.outstandingAmount)}'
                : 'Amount: ${CurrencyService.formatCurrency(bill.amount)}',
            style: TextStyle(
              color: bill.isPartiallyPaid
                  ? Colors.blue.shade700
                  : Colors.red.shade700,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.visibility),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BillDetailsScreen(
                    bill: bill,
                    customer: customer,
                  ),
                ),
              );
            },
            tooltip: 'View Details',
          ),
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: () => _showReminderDialog(customer, [bill]),
            tooltip: 'Send Reminder for this Bill',
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
