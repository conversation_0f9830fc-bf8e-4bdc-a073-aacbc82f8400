import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/services/settings_service.dart';
import 'package:tubewell_water_billing_app/services/permission_service.dart';
import 'package:tubewell_water_billing_app/services/file_service.dart';
import 'package:tubewell_water_billing_app/services/backup_service.dart';
import 'package:tubewell_water_billing_app/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing_app/widgets/backup_dialogs.dart';
import 'package:tubewell_water_billing_app/widgets/permission_error_dialog.dart';
import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'dart:io';

class BackupRestoreSettingsScreen extends StatefulWidget {
  const BackupRestoreSettingsScreen({super.key});

  @override
  State<BackupRestoreSettingsScreen> createState() =>
      _BackupRestoreSettingsScreenState();
}

class _BackupRestoreSettingsScreenState
    extends State<BackupRestoreSettingsScreen> {
  // State variables
  bool _isLoading = true;
  final bool _isBackingUp = false;
  bool _isRestoring = false;
  String _lastBackupDate = 'Never';

  // Backup service
  BackupService? _backupService;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize backup service
      _backupService = BackupService();

      // Load backup settings
      await _loadBackupSettings();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error initializing services: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadBackupSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize settings service
      await SettingsService.initialize();

      // Load last backup date
      final accountId = DatabaseService.getCurrentAccountId();
      final lastBackup =
          await SettingsService.getLastBackupDate(accountId: accountId);

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _lastBackupDate = lastBackup;
        _isLoading = false;
      });
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading backup settings: $e'),
          backgroundColor: Colors.red,
        ),
      );

      setState(() {
        _isLoading = false;
      });
    }
  }

  // Perform the actual backup operation
  Future<void> _performBackup() async {
    if (_backupService == null || !mounted) return;

    // Pass the context to the backupToLocalDevice method
    final result = await _backupService!.backupToLocalDevice(context);

    if (!mounted) return;

    if (result['success']) {
      setState(() {
        _lastBackupDate = DateFormat('MMM d, yyyy HH:mm').format(DateTime.now());
      });

      final snackBar = SnackBar(
        content: Text('Backup saved to: ${result['path']}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'VIEW',
          textColor: Colors.white,
          onPressed: () async {
            // Try to open the file location
            final success = await FileService.openFileLocation(result['path']);

            if (!success && mounted) {
              // If that fails, try to open the Downloads folder directly
              final downloadsPath = '/storage/emulated/0/Download';
              final downloadsSuccess = await FileService.openFileLocation(downloadsPath);

              if (!downloadsSuccess && mounted) {
                // If that also fails, show a more helpful error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Backup saved to: ${result['path']}\nCould not open file location automatically.'),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 8),
                  ),
                );
              }
            }
          },
        ),
      );

      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    } else {
      // Check if the error is related to permissions
      if (result['error'].toString().contains('permission')) {
        // Show our custom permission dialog for permission-related errors
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required to save backups to your device.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Try backup again after permission is granted
              _performBackup();
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );
      } else {
        // For other errors, show a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Backup failed: ${result['error'] ?? 'Please check storage permissions and try again.'}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _restoreBackup() async {
    setState(() {
      _isRestoring = true;
    });

    try {
      // Check storage permission status first without showing system dialog
      final permissionStatus = await PermissionService.checkStoragePermissionStatus();

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // If permission is not granted, show our custom dialog
      if (!permissionStatus) {
        setState(() {
          _isRestoring = false;
        });

        // Show our custom permission dialog
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required to restore backups from your device.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Restart the restore process after permission is granted
              _restoreBackup();
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );

        return;
      }

      // First, try to open the Downloads folder to help the user find the backup
      if (Platform.isAndroid) {
        // Show a message to the user about where to look for backups
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a backup file from the Downloads/tubewell_backups folder'),
            duration: Duration(seconds: 3),
          ),
        );

        // Give the snackbar time to show before opening file picker
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Pick backup file
      FilePickerResult? result;
      try {
        // Try to set initial directory to Downloads folder on Android
        String? initialDirectory;
        if (Platform.isAndroid) {
          // Standard Android Downloads directory
          initialDirectory = '/storage/emulated/0/Download';

          // Check if tubewell_backups folder exists in Downloads
          final backupsDir = Directory('$initialDirectory/tubewell_backups');
          if (!await backupsDir.exists()) {
            // If not, just use Downloads folder
            initialDirectory = '/storage/emulated/0/Download';
          } else {
            // If it exists, use the backups folder
            initialDirectory = backupsDir.path;
          }
        }

        result = await FilePicker.platform.pickFiles(
          type: FileType.any, // Use any file type instead of custom with db extension
          initialDirectory: initialDirectory,
        );
      } catch (e) {
        // Try again with more basic options
        result = await FilePicker.platform.pickFiles();
      }

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      if (result == null || result.files.single.path == null) {
        setState(() {
          _isRestoring = false;
        });
        return;
      }

      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Confirm Restore'),
          content: const Text(
              'Restoring will replace all current data with the backup data. '
              'This action cannot be undone. Do you want to continue?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Restore'),
            ),
          ],
        ),
      );

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      if (confirmed != true) {
        setState(() {
          _isRestoring = false;
        });
        return;
      }

      final backupFilePath = result.files.single.path!;

      // Check if backup file exists and is valid
      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        throw Exception('Backup file not found');
      }

      final backupFileSize = await backupFile.length();

      if (backupFileSize <= 0) {
        throw Exception('Backup file is empty or corrupted');
      }

      // Get database path
      final dbPath = await DatabaseService.getDatabasePath();

      // Close the database before restoring
      await DatabaseService.closeDatabase();

      // Copy backup to database location
      try {
        await backupFile.copy(dbPath);
      } catch (e) {
        throw Exception('Failed to copy backup file: $e');
      }

      // Verify the database file was created
      final newDbFile = File(dbPath);
      if (!await newDbFile.exists()) {
        throw Exception('Failed to create database file');
      }

      // First, completely reset the database service
      await DatabaseService.closeDatabase();

      // Wait a moment to ensure the database is fully closed
      await Future.delayed(const Duration(milliseconds: 500));

      // Open the restored database to extract the account ID
      String accountId = '';
      try {
        // Open the database directly without using DatabaseService
        final db = await openDatabase(dbPath);

        // Try to get the account ID from the settings table
        try {
          final List<Map<String, dynamic>> settings = await db.query(
            'settings',
            where: 'key = ?',
            whereArgs: ['currentAccountId'],
            limit: 1,
          );

          if (settings.isNotEmpty && settings.first['value'] != null) {
            accountId = settings.first['value'] as String;
            debugPrint('Found account ID in settings: $accountId');
          }
        } catch (e) {
          debugPrint('Error querying settings table: $e');
        }

        // If we couldn't get the account ID from settings, try to get it from customers or bills
        if (accountId.isEmpty) {
          try {
            final List<Map<String, dynamic>> customers = await db.query(
              'customers',
              columns: ['accountId'],
              limit: 1,
            );

            if (customers.isNotEmpty && customers.first['accountId'] != null) {
              accountId = customers.first['accountId'] as String;
              debugPrint('Found account ID in customers: $accountId');
            }
          } catch (e) {
            debugPrint('Error querying customers table: $e');
          }
        }

        // Close the database
        await db.close();
      } catch (e) {
        debugPrint('Error extracting account ID from database: $e');
      }

      // If we still don't have an account ID, generate a new one
      if (accountId.isEmpty) {
        accountId = const Uuid().v4();
        debugPrint('Generated new account ID: $accountId');
      }

      // Update the account ID in SharedPreferences
      // This is critical for the app to use the correct account ID after restart
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('currentAccountId', accountId);

        // Also check if we need to create or update the account in the accounts list
        final accountsJson = prefs.getStringList('accounts') ?? [];
        bool accountExists = false;

        // Check if the account already exists in the list
        for (int i = 0; i < accountsJson.length; i++) {
          try {
            final accountMap = jsonDecode(accountsJson[i]);
            if (accountMap['id'] == accountId) {
              accountExists = true;
              break;
            }
          } catch (e) {
            // Skip invalid JSON
          }
        }

        // If the account doesn't exist, add it
        if (!accountExists) {
          final newAccount = {
            'id': accountId,
            'name': 'Restored Account',
            'createdAt': DateTime.now().toIso8601String(),
            'lastAccessed': DateTime.now().toIso8601String(),
          };
          accountsJson.add(jsonEncode(newAccount));
          await prefs.setStringList('accounts', accountsJson);
        }
      } catch (e) {
        // Log the error but continue with the restore process
        debugPrint('Error updating SharedPreferences: $e');
      }

      // Reopen the database with the extracted account ID
      final dbService = DatabaseService();
      await dbService.initializeDatabase(accountId: accountId);

      // Also save the account ID in the database settings for completeness
      try {
        await SettingsService.saveSetting('currentAccountId', accountId);
      } catch (e) {
        // Ignore errors in this approach
      }

      // Notify all screens to refresh their data
      DataChangeNotifierService().notifyDataChanged(DataChangeType.all);

      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      // Show success message with restart recommendation
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Restore Successful'),
          content: const Text(
            'Database restored successfully. For best results, please restart the app now to ensure all data is properly loaded.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    } catch (e) {
      // Try to reopen the database to prevent app from crashing
      try {
        // Just use the current account ID for recovery
        final accountId = DatabaseService.getCurrentAccountId();
        final dbService = DatabaseService();
        await dbService.initializeDatabase(accountId: accountId);
      } catch (reopenError) {
        // Ignore reopen errors
      }

      // Check if widget is still mounted before showing error
      if (!mounted) return;

      // Check if the error is related to permissions
      if (e.toString().contains('permission')) {
        // Show our custom permission dialog for permission-related errors
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required to restore backups from your device.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Restart the restore process after permission is granted
              _restoreBackup();
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );
      } else {
        // For other errors, show a snackbar with retry option
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error restoring backup: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'RETRY',
              textColor: Colors.white,
              onPressed: () {
                _restoreBackup();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRestoring = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Backup & Restore',
        showPdfOption: false,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                _buildBackupInfo(),
                const SizedBox(height: 20),
                _buildManualBackupOptions(),
                const SizedBox(height: 20),
                _buildScheduledBackupOptions(),
                const SizedBox(height: 20),
                _buildRestoreOptions(),
              ],
            ),
    );
  }

  Widget _buildBackupInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Backup Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Regular backups help protect your data from loss. You can create manual backups or set up scheduled backups.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text('Last backup: $_lastBackupDate'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManualBackupOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Manual Backup',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Create a backup manually to your device.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isBackingUp
                      ? null
                      : () async {
                          if (_backupService != null) {
                            // Check permission status first
                            final permissionStatus = await PermissionService.checkStoragePermissionStatus();

                            if (!mounted) return;

                            // If permission is not granted, show our custom dialog
                            if (!permissionStatus) {
                              // Show our custom permission dialog
                              await showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) => PermissionErrorDialog(
                                  title: 'Storage Permission Required',
                                  message: 'Storage permission is required to save backups to your device.',
                                  permissionType: 'storage',
                                  onPermissionGranted: () async {
                                    // Try backup again after permission is granted
                                    if (_backupService != null && mounted) {
                                      _performBackup();
                                    }
                                  },
                                  onCancel: () {
                                    // Do nothing, dialog will be dismissed
                                  },
                                ),
                              );
                              return;
                            }

                            // Perform the backup
                            _performBackup();
                          }
                        },
                  icon: const Icon(Icons.phone_android),
                  label: const Text('Backup to Device'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduledBackupOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scheduled Backup',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Set up automatic backups at regular intervals.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            if (_backupService != null)
              ElevatedButton.icon(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => ScheduledBackupDialog(
                      backupService: _backupService!,
                    ),
                  );
                },
                icon: const Icon(Icons.schedule),
                label: const Text('Configure Scheduled Backup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRestoreOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Restore Backup',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Restore your data from a previous backup file.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isRestoring ? null : _restoreBackup,
                    icon: _isRestoring
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.restore),
                    label: const Text('Restore from Backup'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _checkDatabaseStatus,
                  icon: const Icon(Icons.info_outline),
                  label: const Text('Check Status'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Method to check database status
  Future<void> _checkDatabaseStatus() async {
    try {
      // Get database information
      final db = await DatabaseService.database;

      // Count records in different tables
      int billCount = 0;
      int paymentCount = 0;
      int customerCount = 0;
      int expenseCount = 0;

      try {
        // Get counts from different tables
        final billResult = await db.rawQuery('SELECT COUNT(*) as count FROM bills');
        billCount = billResult.first['count'] as int? ?? 0;

        final paymentResult = await db.rawQuery('SELECT COUNT(*) as count FROM payments');
        paymentCount = paymentResult.first['count'] as int? ?? 0;

        final customerResult = await db.rawQuery('SELECT COUNT(*) as count FROM customers');
        customerCount = customerResult.first['count'] as int? ?? 0;

        // Check if expenses table exists before querying
        final tablesResult = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'");
        if (tablesResult.isNotEmpty) {
          final expenseResult = await db.rawQuery('SELECT COUNT(*) as count FROM expenses');
          expenseCount = expenseResult.first['count'] as int? ?? 0;
        }

        // Show database summary in a dialog
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Database Summary'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Bills: $billCount'),
                  Text('Payments: $paymentCount'),
                  Text('Customers: $customerCount'),
                  Text('Expenses: $expenseCount'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        }
      } catch (e) {
        // Show error in dialog
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Error'),
              content: Text('Could not retrieve database information.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      // Show simple error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Error'),
            content: Text('Could not access database.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    }
  }


}
