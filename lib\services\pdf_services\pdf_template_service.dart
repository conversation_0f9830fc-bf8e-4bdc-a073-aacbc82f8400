import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_utility_service.dart';

/// Template types for different document styles
enum TemplateType {
  standard,
  invoice,
  report,
  statement,
  modern,
  custom
}

/// Header styles for different document types
enum HeaderStyle {
  standard,
  compact,
  detailed,
  minimal,
  modern,
  custom
}

/// Footer styles for different document types
enum FooterStyle {
  standard,
  compact,
  detailed,
  minimal,
  modern,
  custom
}

/// Content section styles for different document types
enum ContentStyle {
  standard,
  twoColumn,
  threeColumn,
  grid,
  cards,
  modern,
  custom
}

/// Service for creating PDF templates with customizable headers, footers, and content sections
class PdfTemplateService {

  /// Create a PDF document with a template
  static Future<pw.Document> createPdfWithTemplate({
    required String title,
    String? subtitle,
    TemplateType templateType = TemplateType.standard,
    HeaderStyle headerStyle = HeaderStyle.standard,
    FooterStyle footerStyle = FooterStyle.standard,
    ContentStyle contentStyle = ContentStyle.standard,
    Map<String, dynamic>? headerData,
    Map<String, dynamic>? footerData,
    pw.MemoryImage? logoImage,
    PdfColor? primaryColor,
    PdfColor? accentColor,
    PdfColor? textColor,
    bool includePageNumbers = true,
    bool includeTimestamp = true,
    String? watermarkText,
  }) async {
    // Load fonts
    await PdfBaseService.loadFonts();

    // Create a PDF document
    final pdf = PdfBaseService.createDocument();

    // Set default colors if not provided
    primaryColor ??= PdfBaseService.primaryColor;
    accentColor ??= PdfBaseService.accentColor;
    textColor ??= PdfBaseService.textColor;

    // Try to load logo if not provided
    if (logoImage == null) {
      try {
        logoImage = await PdfUtilityService.getLogoImage();
      } catch (_) {
        // Ignore if logo can't be loaded
      }
    }

    // Add watermark if provided
    if (watermarkText != null && watermarkText.isNotEmpty) {
      return await PdfUtilityService.addWatermark(pdf, watermarkText);
    }

    // Return the document
    return pdf;
  }

  /// Create a content section with a specific style
  static List<pw.Widget> createContentSection({
    required ContentStyle contentStyle,
    required List<pw.Widget> contentWidgets,
    Map<String, dynamic>? contentData,
    PdfColor? primaryColor,
    PdfColor? accentColor,
    PdfColor? textColor,
  }) {
    // Set default colors if not provided
    primaryColor ??= PdfBaseService.primaryColor;
    accentColor ??= PdfBaseService.accentColor;
    textColor ??= PdfBaseService.textColor;

    switch (contentStyle) {
      case ContentStyle.standard:
        return contentWidgets;

      case ContentStyle.twoColumn:
        // Split content into two columns
        final leftColumn = <pw.Widget>[];
        final rightColumn = <pw.Widget>[];

        for (var i = 0; i < contentWidgets.length; i++) {
          if (i % 2 == 0) {
            leftColumn.add(contentWidgets[i]);
          } else {
            rightColumn.add(contentWidgets[i]);
          }
        }

        return [
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: leftColumn,
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: rightColumn,
                ),
              ),
            ],
          ),
        ];

      case ContentStyle.threeColumn:
        // Split content into three columns
        final column1 = <pw.Widget>[];
        final column2 = <pw.Widget>[];
        final column3 = <pw.Widget>[];

        for (var i = 0; i < contentWidgets.length; i++) {
          if (i % 3 == 0) {
            column1.add(contentWidgets[i]);
          } else if (i % 3 == 1) {
            column2.add(contentWidgets[i]);
          } else {
            column3.add(contentWidgets[i]);
          }
        }

        return [
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: column1,
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: column2,
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: column3,
                ),
              ),
            ],
          ),
        ];

      case ContentStyle.grid:
        // Create a grid layout with 2 columns
        final List<pw.Widget> gridItems = [];

        for (var i = 0; i < contentWidgets.length; i += 2) {
          final List<pw.Widget> rowChildren = [
            pw.Expanded(child: contentWidgets[i]),
          ];

          if (i + 1 < contentWidgets.length) {
            rowChildren.add(pw.SizedBox(width: 10));
            rowChildren.add(pw.Expanded(child: contentWidgets[i + 1]));
          } else {
            rowChildren.add(pw.SizedBox(width: 10));
            rowChildren.add(pw.Expanded(child: pw.Container()));
          }

          gridItems.add(
            pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 10),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: rowChildren,
              ),
            ),
          );
        }

        return gridItems;

      case ContentStyle.cards:
        // Create a card layout with modern styling
        final List<pw.Widget> cardItems = [];

        for (var widget in contentWidgets) {
          if (widget is pw.NewPage) {
            cardItems.add(widget);
          } else {
            cardItems.add(
              pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
              decoration: pw.BoxDecoration(
                color: PdfColors.white,
                borderRadius: pw.BorderRadius.circular(8),
                boxShadow: [
                  pw.BoxShadow(
                    color: PdfBaseService.colorWithOpacity(PdfColor.fromHex('#000000'), 0.1),
                    blurRadius: 3,
                    offset: const PdfPoint(0, 2),
                  ),
                ],
              ),
              padding: const pw.EdgeInsets.all(15),
                child: widget,
              ),
            );
          }
        }

        return cardItems;

      case ContentStyle.modern:
        // Create a modern layout with sections and cards
        final List<pw.Widget> modernItems = [];

        // Group widgets into sections (assuming each section has a title widget followed by content)
        for (var widget in contentWidgets) { // Changed loop to iterate directly over widgets
          if (widget is pw.NewPage) {
            modernItems.add(widget);
          } else {
            // Add the widget with modern styling
            modernItems.add(
              pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                decoration: pw.BoxDecoration(
                  color: PdfColors.white,
                  borderRadius: pw.BorderRadius.circular(8),
                  boxShadow: [
                    pw.BoxShadow(
                      color: PdfBaseService.colorWithOpacity(PdfColor.fromHex('#000000'), 0.1),
                      blurRadius: 3,
                      offset: const PdfPoint(0, 2),
                    ),
                  ],
                ),
                padding: const pw.EdgeInsets.all(15),
                child: widget, // Use the widget directly
              ),
            );
          }
        }

        return modernItems;

      case ContentStyle.custom:
        // Custom content layout based on contentData
        if (contentData != null && contentData.containsKey('customLayout')) {
          return contentData['customLayout'] as List<pw.Widget>;
        }
        // Fallback to standard layout
        return contentWidgets;
    }
  }

  /// Add a page to the PDF document with the template
  static Future<void> addPageWithTemplate({
    required pw.Document pdf,
    required List<pw.Widget> contentWidgets,
    required String title,
    String? subtitle,
    TemplateType templateType = TemplateType.standard,
    HeaderStyle headerStyle = HeaderStyle.standard,
    FooterStyle footerStyle = FooterStyle.standard,
    ContentStyle contentStyle = ContentStyle.standard,
    Map<String, dynamic>? headerData,
    Map<String, dynamic>? footerData,
    pw.MemoryImage? logoImage,
    PdfColor? primaryColor,
    PdfColor? accentColor,
    PdfColor? textColor,
    bool includePageNumbers = true,
    bool includeTimestamp = true,
  }) async {
    // Set default colors if not provided
    primaryColor ??= PdfBaseService.primaryColor;
    accentColor ??= PdfBaseService.accentColor;
    textColor ??= PdfBaseService.textColor;

    // Create header widget based on style
    final headerWidget = await _buildHeader(
      title: title,
      subtitle: subtitle,
      headerStyle: headerStyle,
      headerData: headerData,
      logoImage: logoImage,
      primaryColor: primaryColor,
      accentColor: accentColor,
      textColor: textColor,
    );

    // Create footer widget based on style
    final footerWidget = _buildFooter(
      footerStyle: footerStyle,
      footerData: footerData,
      primaryColor: primaryColor,
      accentColor: accentColor,
      textColor: textColor,
      includePageNumbers: includePageNumbers,
      includeTimestamp: includeTimestamp,
    );

    // Add page with content
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => headerWidget,
        footer: (context) => footerWidget,
        build: (context) => contentWidgets,
      ),
    );
  }

  /// Build header widget based on style
  static Future<pw.Widget> _buildHeader({
    required String title,
    String? subtitle,
    HeaderStyle headerStyle = HeaderStyle.standard,
    Map<String, dynamic>? headerData,
    pw.MemoryImage? logoImage,
    PdfColor? primaryColor,
    PdfColor? accentColor,
    PdfColor? textColor,
  }) async {
    // Set default colors if not provided
    primaryColor ??= PdfBaseService.primaryColor;
    accentColor ??= PdfBaseService.accentColor;
    textColor ??= PdfBaseService.textColor;

    switch (headerStyle) {
      case HeaderStyle.standard:
        return pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 20),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    title.toUpperCase(),
                    style: pw.TextStyle(
                      font: PdfBaseService.boldFont,
                      fontSize: 24,
                      color: primaryColor,
                    ),
                  ),
                  if (subtitle != null)
                    pw.Text(
                      subtitle,
                      style: pw.TextStyle(
                        font: PdfBaseService.italicFont,
                        fontSize: 12,
                        color: accentColor,
                      ),
                    ),
                ],
              ),
              if (logoImage != null)
                pw.Container(
                  height: 50,
                  width: 50,
                  child: pw.Image(logoImage),
                ),
            ],
          ),
        );

      case HeaderStyle.compact:
        return pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 10),
          decoration: pw.BoxDecoration(
            border: pw.Border(bottom: pw.BorderSide(color: accentColor, width: 1)),
          ),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                title,
                style: pw.TextStyle(
                  font: PdfBaseService.boldFont,
                  fontSize: 16,
                  color: primaryColor,
                ),
              ),
              if (subtitle != null)
                pw.Text(
                  subtitle,
                  style: pw.TextStyle(
                    font: PdfBaseService.italicFont,
                    fontSize: 10,
                    color: textColor,
                  ),
                ),
            ],
          ),
        );

      case HeaderStyle.detailed:
        final companyName = headerData?['companyName'] as String? ?? 'Tubewell Water Billing';
        final address = headerData?['address'] as String? ?? '';
        final phone = headerData?['phone'] as String? ?? '';
        final email = headerData?['email'] as String? ?? '';

        return pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 20),
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: PdfBaseService.lightBlue,
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
          ),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      companyName,
                      style: pw.TextStyle(
                        font: PdfBaseService.boldFont,
                        fontSize: 18,
                        color: primaryColor,
                      ),
                    ),
                    if (address.isNotEmpty)
                      pw.Text(
                        address,
                        style: pw.TextStyle(
                          font: PdfBaseService.regularFont,
                          fontSize: 10,
                          color: textColor,
                        ),
                      ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      children: [
                        if (phone.isNotEmpty)
                          pw.Text(
                            'Phone: $phone',
                            style: pw.TextStyle(
                              font: PdfBaseService.regularFont,
                              fontSize: 10,
                              color: textColor,
                            ),
                          ),
                        if (phone.isNotEmpty && email.isNotEmpty)
                          pw.SizedBox(width: 10),
                        if (email.isNotEmpty)
                          pw.Text(
                            'Email: $email',
                            style: pw.TextStyle(
                              font: PdfBaseService.regularFont,
                              fontSize: 10,
                              color: textColor,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  pw.Text(
                    title,
                    style: pw.TextStyle(
                      font: PdfBaseService.boldFont,
                      fontSize: 16,
                      color: accentColor,
                    ),
                  ),
                  if (subtitle != null)
                    pw.Text(
                      subtitle,
                      style: pw.TextStyle(
                        font: PdfBaseService.italicFont,
                        fontSize: 10,
                        color: textColor,
                      ),
                    ),
                  if (logoImage != null)
                    pw.Container(
                      height: 40,
                      width: 40,
                      margin: const pw.EdgeInsets.only(top: 5),
                      child: pw.Image(logoImage),
                    ),
                ],
              ),
            ],
          ),
        );

      case HeaderStyle.minimal:
        return pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 10),
          child: pw.Text(
            title,
            style: pw.TextStyle(
              font: PdfBaseService.boldFont,
              fontSize: 14,
              color: primaryColor,
            ),
          ),
        );

      case HeaderStyle.modern:
        final companyName = headerData?['companyName'] as String? ?? 'Tubewell Water Billing';
        final address = headerData?['address'] as String? ?? '';
        final phone = headerData?['phone'] as String? ?? '';
        final email = headerData?['email'] as String? ?? '';

        return pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 20),
          child: pw.Column(
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  borderRadius: pw.BorderRadius.circular(10),
                  gradient: pw.LinearGradient(
                    colors: [
                      primaryColor,
                      PdfBaseService.colorWithOpacity(primaryColor, 0.7),
                    ],
                    begin: pw.Alignment.topLeft,
                    end: pw.Alignment.bottomRight,
                  ),
                  boxShadow: [
                    pw.BoxShadow(
                      color: PdfBaseService.colorWithOpacity(PdfColor.fromHex('#000000'), 0.2),
                      blurRadius: 5,
                      offset: const PdfPoint(0, 3),
                    ),
                  ],
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            companyName,
                            style: pw.TextStyle(
                              font: PdfBaseService.boldFont,
                              fontSize: 20,
                              color: PdfColors.white,
                            ),
                          ),
                          pw.SizedBox(height: 5),
                          if (address.isNotEmpty)
                            pw.Text(
                              address,
                              style: pw.TextStyle(
                                font: PdfBaseService.regularFont,
                                fontSize: 10,
                                color: PdfBaseService.colorWithOpacity(PdfColors.white, 0.9),
                              ),
                            ),
                          pw.SizedBox(height: 5),
                          pw.Row(
                            children: [
                              if (phone.isNotEmpty)
                                pw.Text(
                                  'Phone: $phone',
                                  style: pw.TextStyle(
                                    font: PdfBaseService.regularFont,
                                    fontSize: 9,
                                    color: PdfBaseService.colorWithOpacity(PdfColors.white, 0.9),
                                  ),
                                ),
                              if (phone.isNotEmpty && email.isNotEmpty)
                                pw.SizedBox(width: 10),
                              if (email.isNotEmpty)
                                pw.Text(
                                  'Email: $email',
                                  style: pw.TextStyle(
                                    font: PdfBaseService.regularFont,
                                    fontSize: 9,
                                    color: PdfBaseService.colorWithOpacity(PdfColors.white, 0.9),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    pw.SizedBox(width: 20),
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        pw.Container(
                          padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                          decoration: pw.BoxDecoration(
                            color: PdfColors.white,
                            borderRadius: pw.BorderRadius.circular(20),
                          ),
                          child: pw.Text(
                            title,
                            style: pw.TextStyle(
                              font: PdfBaseService.boldFont,
                              fontSize: 14,
                              color: primaryColor,
                            ),
                          ),
                        ),
                        pw.SizedBox(height: 5),
                        if (subtitle != null)
                          pw.Text(
                            subtitle,
                            style: pw.TextStyle(
                              font: PdfBaseService.italicFont,
                              fontSize: 10,
                              color: PdfColors.white,
                            ),
                          ),
                        if (logoImage != null)
                          pw.Container(
                            height: 40,
                            width: 40,
                            margin: const pw.EdgeInsets.only(top: 5),
                            decoration: pw.BoxDecoration(
                              color: PdfColors.white,
                              shape: pw.BoxShape.circle,
                            ),
                            padding: const pw.EdgeInsets.all(5),
                            child: pw.Image(logoImage),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Container(
                padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 15),
                decoration: pw.BoxDecoration(
                  color: PdfBaseService.lightBlue,
                  borderRadius: pw.BorderRadius.circular(5),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'Date: ${DateFormat('dd MMM yyyy').format(DateTime.now())}',
                      style: pw.TextStyle(
                        font: PdfBaseService.regularFont,
                        fontSize: 9,
                        color: textColor,
                      ),
                    ),
                    pw.Text(
                      'Reference: ${headerData?['reference'] ?? 'N/A'}',
                      style: pw.TextStyle(
                        font: PdfBaseService.regularFont,
                        fontSize: 9,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );

      case HeaderStyle.custom:
        // Custom header implementation based on headerData
        if (headerData != null && headerData.containsKey('customHeader')) {
          return headerData['customHeader'] as pw.Widget;
        }
        // Fallback to standard header
        return await _buildHeader(
          title: title,
          subtitle: subtitle,
          headerStyle: HeaderStyle.standard,
          logoImage: logoImage,
          primaryColor: primaryColor,
          accentColor: accentColor,
          textColor: textColor,
        );
    }
  }

  /// Create a section title widget
  static pw.Widget createSectionTitle(
    String title, {
    PdfColor? color,
    double fontSize = 14,
    bool addDivider = true,
    PdfColor? dividerColor,
  }) {
    color ??= PdfBaseService.primaryColor;
    dividerColor ??= color;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title.toUpperCase(),
          style: pw.TextStyle(
            font: PdfBaseService.boldFont,
            fontSize: fontSize,
            color: color,
          ),
        ),
        if (addDivider)
          pw.Container(
            margin: const pw.EdgeInsets.only(top: 2, bottom: 10),
            height: 1,
            color: dividerColor,
          ),
      ],
    );
  }

  /// Create a data table widget
  static pw.Widget createDataTable({
    required List<String> headers,
    required List<List<String>> data,
    List<double>? columnWidths,
    PdfColor? headerColor,
    PdfColor? alternateColor,
    PdfColor? borderColor,
    double cellPadding = 5,
    bool zebra = true,
  }) {
    headerColor ??= PdfBaseService.lightGreen;
    alternateColor ??= PdfColor.fromHex('#F5F5F5');
    borderColor ??= PdfBaseService.borderColor;

    // Create column widths map
    final Map<int, pw.TableColumnWidth> columnWidthsMap = {};

    if (columnWidths != null) {
      for (var i = 0; i < columnWidths.length; i++) {
        if (i < headers.length) {
          columnWidthsMap[i] = pw.FixedColumnWidth(columnWidths[i]);
        }
      }
    }

    // Fill remaining columns with flex width
    for (var i = 0; i < headers.length; i++) {
      if (!columnWidthsMap.containsKey(i)) {
        columnWidthsMap[i] = const pw.FlexColumnWidth();
      }
    }

    // Create table rows
    final List<pw.TableRow> rows = [];

    // Add header row
    rows.add(
      pw.TableRow(
        decoration: pw.BoxDecoration(
          color: headerColor,
        ),
        children: headers.map((header) {
          return pw.Padding(
            padding: pw.EdgeInsets.all(cellPadding),
            child: pw.Text(
              header,
              style: pw.TextStyle(
                font: PdfBaseService.boldFont,
                fontSize: 10,
              ),
            ),
          );
        }).toList(),
      ),
    );

    // Add data rows
    for (var i = 0; i < data.length; i++) {
      final rowColor = zebra && i % 2 == 1 ? alternateColor : null;

      rows.add(
        pw.TableRow(
          decoration: rowColor != null
              ? pw.BoxDecoration(color: rowColor)
              : null,
          children: data[i].map((cell) {
            return pw.Padding(
              padding: pw.EdgeInsets.all(cellPadding),
              child: pw.Text(
                cell,
                style: pw.TextStyle(
                  font: PdfBaseService.regularFont,
                  fontSize: 9,
                ),
              ),
            );
          }).toList(),
        ),
      );
    }

    return pw.Table(
      border: pw.TableBorder.all(
        color: borderColor,
        width: 0.5,
      ),
      columnWidths: columnWidthsMap,
      children: rows,
    );
  }

  /// Create an info box widget
  static pw.Widget createInfoBox({
    required String title,
    required List<Map<String, String>> items,
    PdfColor? backgroundColor,
    PdfColor? titleColor,
    double padding = 10,
    bool rounded = true,
  }) {
    backgroundColor ??= PdfBaseService.lightBlue;
    titleColor ??= PdfBaseService.primaryColor;

    return pw.Container(
      padding: pw.EdgeInsets.all(padding),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: rounded
            ? const pw.BorderRadius.all(pw.Radius.circular(5))
            : null,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title.toUpperCase(),
            style: pw.TextStyle(
              font: PdfBaseService.boldFont,
              fontSize: 14,
              color: titleColor,
            ),
          ),
          pw.SizedBox(height: 10),
          ...items.map((item) {
            return pw.Row(
              children: [
                pw.Expanded(
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: item.entries.map((entry) {
                      return pw.Row(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Container(
                            width: 80,
                            child: pw.Text(
                              '${entry.key}:',
                              style: pw.TextStyle(
                                font: PdfBaseService.boldFont,
                                fontSize: 11,
                              ),
                            ),
                          ),
                          pw.Expanded(
                            child: pw.Text(
                              entry.value,
                              style: pw.TextStyle(
                                font: PdfBaseService.regularFont,
                                fontSize: 11,
                              ),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  /// Create a styled key-value table widget
  static pw.Widget createStyledKeyValueTable({
    required String title,
    required List<Map<String, String>> items,
    PdfColor? titleColor,
    PdfColor? keyTextColor,
    PdfColor? valueTextColor,
    PdfColor? borderColor,
    PdfColor? rowEvenColor, // Fallback if rowColorsCycle is not provided
    PdfColor? rowOddColor,  // Fallback if rowColorsCycle is not provided
    List<PdfColor>? rowColorsCycle, // For cycling through multiple row colors
    double cellPadding = 6.0, // Increased padding
    double titleFontSize = 14,
    double keyFontSize = 10,
    double valueFontSize = 10,
    bool showTableBorder = false, // Controls full table border
    bool showRowDividers = true, // Controls thin lines between rows
  }) {
    titleColor ??= PdfBaseService.primaryColor;
    keyTextColor ??= PdfBaseService.textColor;
    valueTextColor ??= PdfBaseService.textColor;
    borderColor ??= PdfBaseService.borderColor;

    // Default fallback colors if no cycle is provided
    final effectiveRowEvenColor = rowEvenColor ?? PdfColors.white;
    final effectiveRowOddColor = rowOddColor ?? PdfColor.fromHex('#F5F5F5');

    final List<pw.TableRow> tableRows = [];
    int rowIndex = 0;

    for (final itemMap in items) {
      for (final entry in itemMap.entries) {
        PdfColor currentRowColor;
        if (rowColorsCycle != null && rowColorsCycle.isNotEmpty) {
          currentRowColor = rowColorsCycle[rowIndex % rowColorsCycle.length];
        } else {
          currentRowColor = rowIndex % 2 == 0 ? effectiveRowEvenColor : effectiveRowOddColor;
        }

        tableRows.add(
          pw.TableRow(
            decoration: pw.BoxDecoration(color: currentRowColor),
            children: [
              pw.Padding(
                padding: pw.EdgeInsets.all(cellPadding),
                child: pw.Text(
                  entry.key,
                  style: pw.TextStyle(
                    font: PdfBaseService.boldFont, // Keys are bold
                    fontSize: keyFontSize,
                    color: keyTextColor,
                  ),
                ),
              ),
              pw.Padding(
                padding: pw.EdgeInsets.all(cellPadding),
                child: pw.Text(
                  entry.value,
                  style: pw.TextStyle(
                    font: PdfBaseService.regularFont,
                    fontSize: valueFontSize,
                    color: valueTextColor,
                  ),
                ),
              ),
            ],
          ),
        );
        rowIndex++;
      }
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title.toUpperCase(),
          style: pw.TextStyle(
            font: PdfBaseService.boldFont,
            fontSize: titleFontSize,
            color: titleColor,
          ),
        ),
        pw.SizedBox(height: 8), // Space between title and table
        pw.Table(
          border: showTableBorder
              ? pw.TableBorder.all(color: borderColor, width: 0.5)
              : (showRowDividers
                  ? pw.TableBorder(
                      horizontalInside: pw.BorderSide(color: PdfBaseService.colorWithOpacity(borderColor, 0.5), width: 0.5), // Softer horizontal lines
                    )
                  : null),
          columnWidths: const {
            0: pw.FlexColumnWidth(1.2), // Key column slightly wider or adjust as needed
            1: pw.FlexColumnWidth(2),   // Value column takes more space
          },
          children: tableRows,
        ),
      ],
    );
  }

  /// Create a modern card widget with optional icon and gradient background
  static pw.Widget createModernCard({
    required String title,
    required List<Map<String, String>> items,
    PdfColor? primaryColor,
    PdfColor? secondaryColor,
    PdfColor? textColor,
    double borderRadius = 8,
    double padding = 15,
    bool addShadow = true,
    bool addGradient = true,
  }) {
    primaryColor ??= PdfBaseService.primaryColor;
    secondaryColor ??= PdfBaseService.accentColor;
    textColor ??= PdfColors.white;

    // Create gradient colors for background
    final gradientColors = addGradient
        ? [primaryColor, secondaryColor]
        : [primaryColor, primaryColor];

    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 15),
      decoration: pw.BoxDecoration(
        borderRadius: pw.BorderRadius.circular(borderRadius),
        gradient: pw.LinearGradient(
          colors: gradientColors,
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        boxShadow: addShadow ? [
          pw.BoxShadow(
            color: PdfBaseService.colorWithOpacity(PdfColor.fromHex('#000000'), 0.2),
            blurRadius: 5,
            offset: const PdfPoint(0, 3),
          ),
        ] : null,
      ),
      padding: pw.EdgeInsets.all(padding),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title.toUpperCase(),
            style: pw.TextStyle(
              font: PdfBaseService.boldFont,
              fontSize: 16,
              color: textColor,
            ),
          ),
          pw.Container(
            margin: const pw.EdgeInsets.symmetric(vertical: 8),
            height: 1,
            color: PdfBaseService.colorWithOpacity(textColor, 0.3),
          ),
          ...items.map((item) {
            return pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 8),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: item.entries.map((entry) {
                  return pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          entry.key,
                          style: pw.TextStyle(
                            font: PdfBaseService.regularFont,
                            fontSize: 10,
                            color: PdfBaseService.colorWithOpacity(textColor ?? PdfBaseService.textColor, 0.8),
                          ),
                        ),
                        pw.SizedBox(height: 2),
                        pw.Text(
                          entry.value,
                          style: pw.TextStyle(
                            font: PdfBaseService.boldFont,
                            fontSize: 14,
                            color: textColor,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Create a status badge widget
  static pw.Widget createStatusBadge({
    required String text,
    PdfColor? backgroundColor,
    PdfColor? textColor,
    double fontSize = 10,
    double borderRadius = 12,
    double padding = 5,
  }) {
    backgroundColor ??= PdfBaseService.primaryColor;
    textColor ??= PdfColors.white;

    return pw.Container(
      padding: pw.EdgeInsets.symmetric(horizontal: padding * 2, vertical: padding),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: pw.BorderRadius.circular(borderRadius),
      ),
      child: pw.Text(
        text.toUpperCase(),
        style: pw.TextStyle(
          font: PdfBaseService.boldFont,
          fontSize: fontSize,
          color: textColor,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// Create a modern data table with rounded corners and better styling
  static pw.Widget createModernDataTable({
    required List<String> headers,
    required List<List<String>> data,
    List<double>? columnWidths,
    PdfColor? headerColor,
    PdfColor? borderColor,
    PdfColor? textColor,
    PdfColor? alternateColor,
    double borderRadius = 8,
    double cellPadding = 8,
    bool zebra = true,
    bool addBorder = true,
  }) {
    headerColor ??= PdfBaseService.primaryColor;
    borderColor ??= PdfBaseService.borderColor;
    textColor ??= PdfBaseService.textColor;
    alternateColor ??= PdfColor.fromHex('#F9FAFB'); // Very light gray

    // Create column widths map
    final Map<int, pw.TableColumnWidth> columnWidthsMap = {};

    if (columnWidths != null) {
      for (var i = 0; i < columnWidths.length; i++) {
        if (i < headers.length) {
          columnWidthsMap[i] = pw.FixedColumnWidth(columnWidths[i]);
        }
      }
    }

    // Fill remaining columns with flex width
    for (var i = 0; i < headers.length; i++) {
      if (!columnWidthsMap.containsKey(i)) {
        columnWidthsMap[i] = const pw.FlexColumnWidth();
      }
    }

    // Create table rows
    final List<pw.TableRow> rows = [];

    // Add header row
    rows.add(
      pw.TableRow(
        decoration: pw.BoxDecoration(
          color: headerColor,
          borderRadius: pw.BorderRadius.only(
            topLeft: pw.Radius.circular(borderRadius),
            topRight: pw.Radius.circular(borderRadius),
          ),
        ),
        children: headers.map((header) {
          return pw.Padding(
            padding: pw.EdgeInsets.all(cellPadding),
            child: pw.Text(
              header,
              style: pw.TextStyle(
                font: PdfBaseService.boldFont,
                fontSize: 11,
                color: PdfColors.white,
              ),
              textAlign: pw.TextAlign.center,
            ),
          );
        }).toList(),
      ),
    );

    // Add data rows
    for (var i = 0; i < data.length; i++) {
      final isLastRow = i == data.length - 1;
      final rowColor = zebra && i % 2 == 1 ? alternateColor : null;

      rows.add(
        pw.TableRow(
          decoration: pw.BoxDecoration(
            color: rowColor,
            borderRadius: isLastRow ? pw.BorderRadius.only(
              bottomLeft: pw.Radius.circular(borderRadius),
              bottomRight: pw.Radius.circular(borderRadius),
            ) : null,
          ),
          children: data[i].map((cell) {
            return pw.Padding(
              padding: pw.EdgeInsets.all(cellPadding),
              child: pw.Text(
                cell,
                style: pw.TextStyle(
                  font: PdfBaseService.regularFont,
                  fontSize: 10,
                  color: textColor,
                ),
                textAlign: pw.TextAlign.center,
              ),
            );
          }).toList(),
        ),
      );
    }

    return pw.Container(
      decoration: addBorder ? pw.BoxDecoration(
        borderRadius: pw.BorderRadius.circular(borderRadius),
        border: pw.Border.all(
          color: borderColor,
          width: 0.5,
        ),
      ) : null,
      child: pw.ClipRRect(
        horizontalRadius: borderRadius,
        verticalRadius: borderRadius,
        child: pw.Table(
          border: addBorder ? pw.TableBorder.symmetric(
            inside: pw.BorderSide(
              color: borderColor,
              width: 0.5,
            ),
          ) : null,
          columnWidths: columnWidthsMap,
          children: rows,
        ),
      ),
    );
  }

  /// Build footer widget based on style
  static pw.Widget _buildFooter({
    FooterStyle footerStyle = FooterStyle.standard,
    Map<String, dynamic>? footerData,
    PdfColor? primaryColor,
    PdfColor? accentColor,
    PdfColor? textColor,
    bool includePageNumbers = true,
    bool includeTimestamp = true,
  }) {
    // Set default colors if not provided
    primaryColor ??= PdfBaseService.primaryColor;
    accentColor ??= PdfBaseService.accentColor;
    textColor ??= PdfBaseService.textColor;

    // Get current timestamp
    final timestamp = includeTimestamp
        ? 'Generated: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}'
        : '';

    // Get company name
    final companyName = footerData?['companyName'] as String? ?? 'Tubewell Water Billing';

    // Get custom text
    final customText = footerData?['customText'] as String? ?? '';

    switch (footerStyle) {
      case FooterStyle.standard:
        return pw.Container(
          margin: const pw.EdgeInsets.only(top: 20),
          decoration: pw.BoxDecoration(
            border: pw.Border(top: pw.BorderSide(color: PdfBaseService.borderColor, width: 0.5)),
          ),
          padding: const pw.EdgeInsets.only(top: 8),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                timestamp,
                style: pw.TextStyle(
                  font: PdfBaseService.italicFont,
                  fontSize: 9,
                  color: textColor,
                ),
              ),
              pw.Text(
                companyName,
                style: pw.TextStyle(
                  font: PdfBaseService.italicFont,
                  fontSize: 9,
                  color: textColor,
                ),
              ),
            ],
          ),
        );

      case FooterStyle.compact:
        return pw.Container(
          margin: const pw.EdgeInsets.only(top: 10),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              if (includePageNumbers)
                pw.Container(
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    'Page ${footerData?['pageNumber'] ?? 1} of ${footerData?['pageCount'] ?? 1}',
                    style: pw.TextStyle(
                      font: PdfBaseService.regularFont,
                      fontSize: 8,
                      color: textColor,
                    ),
                  ),
                ),
            ],
          ),
        );

      case FooterStyle.detailed:
        return pw.Container(
          margin: const pw.EdgeInsets.only(top: 20),
          padding: const pw.EdgeInsets.only(top: 8),
          decoration: pw.BoxDecoration(
            border: pw.Border(top: pw.BorderSide(color: accentColor, width: 0.5)),
          ),
          child: pw.Column(
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    timestamp,
                    style: pw.TextStyle(
                      font: PdfBaseService.italicFont,
                      fontSize: 9,
                      color: textColor,
                    ),
                  ),
                  if (includePageNumbers)
                    pw.Text(
                      'Page ${footerData?['pageNumber'] ?? 1} of ${footerData?['pageCount'] ?? 1}',
                      style: pw.TextStyle(
                        font: PdfBaseService.regularFont,
                        fontSize: 9,
                        color: textColor,
                      ),
                    ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  if (customText.isNotEmpty)
                    pw.Text(
                      customText,
                      style: pw.TextStyle(
                        font: PdfBaseService.italicFont,
                        fontSize: 8,
                        color: textColor,
                      ),
                    ),
                  pw.Text(
                    companyName,
                    style: pw.TextStyle(
                      font: PdfBaseService.boldFont,
                      fontSize: 9,
                      color: primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );

      case FooterStyle.minimal:
        return pw.Container(
          margin: const pw.EdgeInsets.only(top: 10),
          alignment: pw.Alignment.centerRight,
          child: pw.Text(
            includePageNumbers
                ? 'Page ${footerData?['pageNumber'] ?? 1}'
                : '',
            style: pw.TextStyle(
              font: PdfBaseService.regularFont,
              fontSize: 8,
              color: textColor,
            ),
          ),
        );

      case FooterStyle.modern:
        return pw.Container(
          margin: const pw.EdgeInsets.only(top: 20),
          child: pw.Column(
            children: [
              pw.Divider(
                color: PdfBaseService.colorWithOpacity(primaryColor, 0.3),
                thickness: 0.5,
              ),
              pw.SizedBox(height: 8),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Row(
                    children: [
                      pw.Container(
                        width: 15,
                        height: 15,
                        decoration: pw.BoxDecoration(
                          color: primaryColor,
                          shape: pw.BoxShape.circle,
                        ),
                        child: pw.Center(
                          child: pw.Text(
                            '©',
                            style: pw.TextStyle(
                              font: PdfBaseService.boldFont,
                              fontSize: 8,
                              color: PdfColors.white,
                            ),
                          ),
                        ),
                      ),
                      pw.SizedBox(width: 5),
                      pw.Text(
                        companyName,
                        style: pw.TextStyle(
                          font: PdfBaseService.boldFont,
                          fontSize: 9,
                          color: primaryColor,
                        ),
                      ),
                    ],
                  ),
                  if (includeTimestamp)
                    pw.Text(
                      timestamp,
                      style: pw.TextStyle(
                        font: PdfBaseService.italicFont,
                        fontSize: 8,
                        color: textColor,
                      ),
                    ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  if (customText.isNotEmpty)
                    pw.Text(
                      customText,
                      style: pw.TextStyle(
                        font: PdfBaseService.italicFont,
                        fontSize: 8,
                        color: textColor,
                      ),
                    ),
                  if (includePageNumbers)
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                      decoration: pw.BoxDecoration(
                        color: PdfBaseService.lightBlue,
                        borderRadius: pw.BorderRadius.circular(10),
                      ),
                      child: pw.Text(
                        'Page ${footerData?['pageNumber'] ?? 1} of ${footerData?['pageCount'] ?? 1}',
                        style: pw.TextStyle(
                          font: PdfBaseService.regularFont,
                          fontSize: 8,
                          color: primaryColor,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        );

      case FooterStyle.custom:
        // Custom footer implementation based on footerData
        if (footerData != null && footerData.containsKey('customFooter')) {
          return footerData['customFooter'] as pw.Widget;
        }
        // Fallback to standard footer
        return _buildFooter(
          footerStyle: FooterStyle.standard,
          footerData: footerData,
          primaryColor: primaryColor,
          accentColor: accentColor,
          textColor: textColor,
          includePageNumbers: includePageNumbers,
          includeTimestamp: includeTimestamp,
        );
    }
  }
}
