import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing_app/providers/pdf_settings_provider.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing_app/screens/settings/settings_screen.dart';
import 'package:tubewell_water_billing_app/utils/navigation_helper.dart';
import 'package:tubewell_water_billing_app/utils/page_transitions.dart';


class TubewellAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final TabBar? bottom;
  final bool showPdfOption;
  final VoidCallback? onPdfPressed;
  final Map<String, dynamic>? pdfData;
  final String currentScreen;
  final bool showDrawer;
  final PdfSettings? pdfSettings;

  const TubewellAppBar({
    super.key,
    this.title = 'Tubewell',
    this.actions,
    this.showBackButton = true,
    this.onBackPressed,
    this.bottom,
    this.showPdfOption = true,
    this.onPdfPressed,
    this.pdfData,
    this.currentScreen = '',
    this.showDrawer = true,
    this.pdfSettings,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> defaultActions = [];

    // Add PDF option if enabled
    if (showPdfOption) {
      defaultActions.add(
        IconButton(
          icon: const Icon(Icons.picture_as_pdf),
          onPressed: onPdfPressed ??
              () {
                // Get global PDF settings
                final globalSettings = Provider.of<PdfSettingsProvider>(context, listen: false);

                // Determine appropriate PDF settings based on current screen
                PdfSettings? settings = pdfSettings;

                if (settings == null) {
                  // Get global settings
                  settings = globalSettings.getCurrentSettings();

                  // Customize settings based on screen type if needed
                  if (currentScreen.contains('bill') || currentScreen.contains('invoice')) {
                    // For invoice-type screens, use invoice template type but keep other settings
                    settings = settings.copyWith(
                      templateType: TemplateType.invoice,
                      headerStyle: HeaderStyle.detailed,
                      footerStyle: FooterStyle.detailed,
                      contentStyle: ContentStyle.cards,
                    );
                  }
                }

                // Handle PDF generation using the universal handler with enhanced options
                UniversalPdfService.handlePdf(
                  context,
                  pdfData,
                  autoOpen: true,
                  showSaveOption: true,
                  showShareOption: true,
                  showPrintOption: true,
                  pdfSettings: settings,
                );
              },
          tooltip: 'Generate PDF',
        ),
      );
    }


    // Add default settings button
    defaultActions.add(
      IconButton(
        icon: const Icon(Icons.settings_outlined),
        onPressed: () {
          NavigationHelper.navigateWithSlide(
            context,
            const SettingsScreenImpl(),
            direction: SlideDirection.left,
          );
        },
        tooltip: 'Settings',
      ),
    );

    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      centerTitle: true,
      backgroundColor: const Color(0xFF2E7D32), // Dark green color
      foregroundColor: Colors.white,
      elevation: 0,
      toolbarHeight: 56.0,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => NavigationHelper.goBack(context),
            )
          : (showDrawer
              ? Builder(
                  builder: (context) => IconButton(
                    icon: const Icon(Icons.menu),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                    tooltip: 'Menu',
                  ),
                )
              : null),
      actions: actions ?? defaultActions,
      bottom: bottom,
    );
  }

  @override
  Size get preferredSize {
    final bottomHeight = bottom?.preferredSize.height ?? 0.0;
    return Size.fromHeight(56.0 + bottomHeight);
  }
}
