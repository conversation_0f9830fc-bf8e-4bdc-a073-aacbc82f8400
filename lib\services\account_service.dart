import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import 'package:tubewell_water_billing_app/models/account.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';

class AccountService {
  static const String _accountsKey = 'accounts';
  static const String _currentAccountIdKey = 'currentAccountId';

  static Account? _currentAccount;
  static List<Account> _accounts = [];
  static bool _isInitialized = false;

  // Get the current account
  static Account? get currentAccount => _currentAccount;

  // Get all accounts
  static List<Account> get accounts => List.unmodifiable(_accounts);

  // Initialize the account service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    final prefs = await SharedPreferences.getInstance();

    // Load accounts
    final accountsJson = prefs.getStringList(_accountsKey);
    if (accountsJson != null) {
      try {
        _accounts = accountsJson
            .map((json) {
              try {
                final map = jsonDecode(json);
                if (map is Map<String, dynamic>) {
                  return Account.fromMap(map);
                } else {
                  // Invalid account data format
                  return null;
                }
              } catch (e) {
                // Error decoding JSON for account
                return null;
              }
            })
            .where((account) => account != null)
            .cast<Account>()
            .toList();
      } catch (e) {
        // Error loading accounts, use empty list
        _accounts = [];
      }
    }

    // If no accounts exist, create a default account
    if (_accounts.isEmpty) {
      final defaultAccount = Account(name: 'Default Account');
      _accounts.add(defaultAccount);
      await _saveAccounts();
    }

    // Load current account ID
    final currentAccountId = prefs.getString(_currentAccountIdKey);
    if (currentAccountId != null) {
      _currentAccount = _accounts.firstWhere(
        (account) => account.id == currentAccountId,
        orElse: () => _accounts.first,
      );
    } else {
      _currentAccount = _accounts.first;
      await _saveCurrentAccountId();
    }

    // Update last accessed time for current account
    _currentAccount!.updateLastAccessed();
    await _saveAccounts();

    _isInitialized = true;
  }

  // Save accounts to shared preferences
  static Future<void> _saveAccounts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accountsJson = _accounts
          .map((account) {
            try {
              final map = account.toMap();
              return jsonEncode(map);
            } catch (e) {
              // Error encoding account to JSON
              return null;
            }
          })
          .where((json) => json != null)
          .cast<String>()
          .toList();

      await prefs.setStringList(_accountsKey, accountsJson);
      // Accounts saved successfully
    } catch (e) {
      // Error saving accounts to SharedPreferences
    }
  }

  // Save current account ID to shared preferences
  static Future<void> _saveCurrentAccountId() async {
    if (_currentAccount == null) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentAccountIdKey, _currentAccount!.id);
  }

  // Add a new account
  static Future<Account> addAccount(String name, {String? description}) async {
    await _ensureInitialized();

    // Validate input
    if (name.isEmpty) {
      throw Exception('Account name cannot be empty');
    }

    // Sanitize input - remove any characters that might cause JSON issues
    final sanitizedName = name.trim();
    final sanitizedDescription = description?.trim();

    // Check if account with this name already exists
    if (_accounts.any((account) => account.name == sanitizedName)) {
      throw Exception('Account with this name already exists');
    }

    try {
      final account = Account(
        name: sanitizedName,
        description: sanitizedDescription,
      );

      _accounts.add(account);
      await _saveAccounts();

      // Successfully added account
      return account;
    } catch (e) {
      // Error adding account
      throw Exception('Failed to add account: $e');
    }
  }

  // Update an account
  static Future<void> updateAccount(Account account) async {
    await _ensureInitialized();

    final index = _accounts.indexWhere((a) => a.id == account.id);
    if (index == -1) {
      throw Exception('Account not found');
    }

    _accounts[index] = account;
    await _saveAccounts();

    // If this is the current account, update the reference
    if (_currentAccount?.id == account.id) {
      _currentAccount = account;
    }
  }

  // Delete an account
  static Future<void> deleteAccount(String accountId) async {
    await _ensureInitialized();

    // Cannot delete the only account
    if (_accounts.length <= 1) {
      throw Exception('Cannot delete the only account');
    }

    // Cannot delete the current account
    if (_currentAccount?.id == accountId) {
      throw Exception(
          'Cannot delete the current account. Switch to another account first.');
    }

    _accounts.removeWhere((account) => account.id == accountId);
    await _saveAccounts();
  }

  // Switch to another account
  static Future<void> switchAccount(String accountId) async {
    final logger = Logger();
    logger.i('Switching to account: $accountId');

    await _ensureInitialized();

    final account = _accounts.firstWhere(
      (account) => account.id == accountId,
      orElse: () => throw Exception('Account not found'),
    );

    // If already on this account, do nothing
    if (_currentAccount?.id == accountId) {
      logger.i('Already on account: $accountId, no switch needed');
      return;
    }

    try {
      // Close current database connection
      logger.i('Closing current database connection');
      await DatabaseService.closeDatabase();

      // Add a small delay to ensure closure is complete
      await Future.delayed(const Duration(milliseconds: 100));

      // Update current account
      _currentAccount = account;
      _currentAccount!.updateLastAccessed();

      // Save changes
      await _saveCurrentAccountId();
      await _saveAccounts();

      // Reinitialize database with new account context
      logger.i('Initializing database for new account: $accountId');
      await DatabaseService.initialize(accountId: accountId);

      logger.i('Account switch completed successfully');
    } catch (e) {
      logger.e('Error switching account: $e');

      // If an error occurs, try to recover by reinitializing the database
      try {
        logger.i('Attempting to recover from account switch error');
        await DatabaseService.closeDatabase();
        await Future.delayed(const Duration(milliseconds: 100));
        await DatabaseService.initialize(accountId: accountId);
      } catch (recoveryError) {
        logger.e('Recovery attempt failed: $recoveryError');
        // If recovery fails, rethrow the original error
        throw Exception('Failed to switch account: $e');
      }
    }
  }

  // Ensure the service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // Get the database name for an account
  static String getDatabaseName(String accountId) {
    return 'account_$accountId.db';
  }
}
