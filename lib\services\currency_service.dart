import 'dart:async';
import 'package:tubewell_water_billing_app/models/currency.dart';
import 'package:tubewell_water_billing_app/services/settings_service.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';

class CurrencyService {
  static Currency? _currentCurrency;

  // Stream controller for currency changes
  static final StreamController<Currency> _currencyChangeController =
      StreamController<Currency>.broadcast();

  // Stream that broadcasts currency changes
  static Stream<Currency> get onCurrencyChanged =>
      _currencyChangeController.stream;

  // Get the current currency
  static Currency get currentCurrency {
    if (_currentCurrency == null) {
      // Default to Pakistani Rupee if not initialized
      return Currencies.pakistaniRupee;
    }
    return _currentCurrency!;
  }

  // Initialize the currency service
  static Future<void> initialize() async {
    // Always reload currency settings even if already initialized
    // This ensures we have the latest settings after app restart

    try {
      final accountId = DatabaseService.getCurrentAccountId();

      // Initialize settings service
      await SettingsService.initialize();

      // Load currency code from settings
      final code = await SettingsService.getCurrencyCode(accountId: accountId);

      // Store the previous currency for comparison
      final previousCurrency = _currentCurrency;

      // Try to find currency by code
      _currentCurrency = Currencies.getByCode(code);

      // If not found, try to load symbol and find by symbol
      if (_currentCurrency == null) {
        final symbol =
            await SettingsService.getCurrencySymbol(accountId: accountId);
        // Try to find currency by symbol
        _currentCurrency = Currencies.getBySymbol(symbol);
      }

      // If still not found, default to Pakistani Rupee
      if (_currentCurrency == null) {
        // Default to Pakistani Rupee
        _currentCurrency = Currencies.pakistaniRupee;

        // Save the default currency to ensure it's available next time
        await saveCurrencyToStorage(Currencies.pakistaniRupee, accountId);
      }

      // Notify listeners if the currency has changed
      if (previousCurrency?.code != _currentCurrency?.code) {
        _currencyChangeController.add(_currentCurrency!);
      }

      // Currency service initialized successfully
    } catch (e) {
      // Error handled by defaulting to Pakistani Rupee
      // Default to Pakistani Rupee in case of any error
      _currentCurrency = Currencies.pakistaniRupee;
    }

    // Currency service is now initialized
  }

  // Helper method to save currency to storage
  static Future<void> saveCurrencyToStorage(
      Currency currency, String? accountId) async {
    try {
      // Initialize settings service if needed
      await SettingsService.initialize();

      // Save currency code and symbol to settings
      await SettingsService.saveCurrencyCode(currency.code,
          accountId: accountId);
      await SettingsService.saveCurrencySymbol(currency.symbol,
          accountId: accountId);
      // Save country code to settings
      await SettingsService.saveCountryCode(currency.countryCode,
          accountId: accountId);
      // Currency saved to settings successfully
    } catch (e) {
      // Error saving currency to settings
    }
  }

  // Get the default hourly rate
  static Future<double> getDefaultHourlyRate() async {
    try {
      final accountId = DatabaseService.getCurrentAccountId();
      await SettingsService.initialize();
      final rateStr = await SettingsService.getDefaultHourlyRate(accountId: accountId);
      return double.tryParse(rateStr) ?? 900.0;
    } catch (e) {
      return 900.0; // Default value if there's an error
    }
  }

  // Save the default hourly rate
  static Future<void> saveDefaultHourlyRate(double rate) async {
    try {
      final accountId = DatabaseService.getCurrentAccountId();
      await SettingsService.initialize();
      await SettingsService.saveDefaultHourlyRate(rate.toString(), accountId: accountId);
    } catch (e) {
      // Error handled silently
    }
  }

  // Set the current currency
  static Future<void> setCurrency(Currency currency) async {
    // Only update if the currency has actually changed
    if (_currentCurrency?.code != currency.code) {
      _currentCurrency = currency;

      final accountId = DatabaseService.getCurrentAccountId();

      // Save currency to storage
      await saveCurrencyToStorage(currency, accountId);

      // Notify listeners about the currency change
      _currencyChangeController.add(currency);
    }
  }

  // Get the current country code
  static String getCountryCode() {
    return _currentCurrency?.countryCode ?? '92'; // Default to Pakistan country code
  }

  // Format an amount with the current currency symbol
  static String formatAmount(double amount, {int decimalPlaces = 2}) {
    final symbol = _currentCurrency?.symbol ?? '₨';
    return '$symbol ${amount.toStringAsFixed(decimalPlaces)}';
  }

  // Format an amount with the current currency symbol and no space
  static String formatAmountCompact(double amount, {int decimalPlaces = 0}) {
    final symbol = _currentCurrency?.symbol ?? '₨';
    return '$symbol${amount.toStringAsFixed(decimalPlaces)}';
  }

  // Global currency formatter that should be used throughout the app
  static String formatCurrency(dynamic amount,
      {int decimalPlaces = 0, bool compact = false}) {
    // Handle various input types
    double value = 0;
    if (amount is int) {
      value = amount.toDouble();
    } else if (amount is double) {
      value = amount;
    } else if (amount is String) {
      value = double.tryParse(amount) ?? 0;
    } else if (amount is num) {
      value = amount.toDouble();
    }

    // Format according to preference (with or without space)
    final symbol = _currentCurrency?.symbol ?? '₨';
    if (compact) {
      return '$symbol${value.toStringAsFixed(decimalPlaces)}';
    } else {
      return '$symbol ${value.toStringAsFixed(decimalPlaces)}';
    }
  }

  // Parse an amount string with currency symbol
  static double? parseAmount(String amountStr) {
    if (amountStr.isEmpty) return null;

    // Remove the currency symbol and any non-numeric characters except decimal point
    final symbol = _currentCurrency?.symbol ?? '₨';
    final cleanStr =
        amountStr.replaceAll(symbol, '').replaceAll(RegExp(r'[^\d.]'), '');

    return double.tryParse(cleanStr);
  }

  // Format currency for compact display in charts and graphs
  static String formatCompactCurrency(double value) {
    final symbol = _currentCurrency?.symbol ?? '₨';

    if (value >= 1000000) {
      return '$symbol${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '$symbol${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return '$symbol${value.toStringAsFixed(0)}';
    }
  }
}
