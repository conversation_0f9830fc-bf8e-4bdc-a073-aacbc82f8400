import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';

class AppInfoScreen extends StatelessWidget {
  const AppInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'App Information',
        showPdfOption: false,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildAppHeader(),
          const SizedBox(height: 20),
          _buildFeaturesList(),
          const SizedBox(height: 20),
          _buildAboutSection(),
        ],
      ),
    );
  }

  Widget _buildAppHeader() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(
              Icons.water_drop,
              size: 64,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            const Text(
              'Tubewell Water Billing App',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.new_releases, size: 16, color: Colors.purple),
                  SizedBox(width: 8),
                  Text('Version: 1.7.0',
                      style: TextStyle(color: Colors.purple)),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'A comprehensive solution for managing tubewell water billing and customer accounts.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Key Features',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              icon: Icons.account_balance_wallet,
              title: 'Multiple Account Support',
              description:
                  'Manage multiple business accounts with complete data isolation.',
              color: Colors.blue,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.currency_exchange,
              title: 'Customizable Currency',
              description:
                  'Set your preferred currency for all financial transactions.',
              color: Colors.green,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.backup,
              title: 'Backup & Restore',
              description:
                  'Secure your data with local and cloud backup options.',
              color: Colors.orange,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.picture_as_pdf,
              title: 'PDF Generation',
              description:
                  'Create professional bills and reports in PDF format.',
              color: Colors.red,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.credit_card,
              title: 'Customer Credit Management',
              description:
                  'Track customer credits and apply them to future bills.',
              color: Colors.purple,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.analytics,
              title: 'Financial Reports',
              description:
                  'Generate detailed reports on income, expenses, and customer balances.',
              color: Colors.teal,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(26),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'About',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'The Tubewell Water Billing App is designed to help tubewell operators manage their billing, customers, and finances efficiently. With features like multiple account support, customizable currency settings, and comprehensive reporting, it provides a complete solution for water billing management.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            const Text(
              'This app is built with Flutter and uses SQLite for local data storage, ensuring your data is secure and accessible even without an internet connection.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '© 2023 All Rights Reserved',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
