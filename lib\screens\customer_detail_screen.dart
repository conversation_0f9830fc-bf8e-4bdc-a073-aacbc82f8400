import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/models/bill.dart';
import 'package:tubewell_water_billing_app/models/payment.dart';
import 'package:tubewell_water_billing_app/models/currency.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/services/payment_service.dart';
import 'package:tubewell_water_billing_app/services/billing_service.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing_app/widgets/empty_state_widget.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing_app/screens/bill_details_screen.dart';
import 'package:tubewell_water_billing_app/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing_app/forms/auto_payment_form_screen.dart';
import 'dart:async';
import 'package:tubewell_water_billing_app/screens/payments_screen.dart';
import 'package:tubewell_water_billing_app/widgets/info_chips.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';
import 'package:tubewell_water_billing_app/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing_app/widgets/reminder_dialog.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing_app/utils/navigation_helper.dart';
import 'package:pdf/pdf.dart';

class CustomerDetailScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailScreen({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Summary variables
  bool _isLoadingSummary = true;
  double _totalBilledAmount = 0;
  double _totalPaidAmount = 0;
  double _netBalance = 0;

  // Create keys for each tab to directly access their state
  final GlobalKey<_CustomerTransactionsTabState> _transactionsTabKey =
      GlobalKey();
  final GlobalKey<_CustomerPaymentsTabState> _paymentsTabKey = GlobalKey();

  // Stream subscription for currency changes
  late final StreamSubscription<Currency> _currencySubscription;

  // Data change subscription
  late final StreamSubscription<DataChangeType> _dataChangeSubscription;

  // Store the tab controller listener function
  void _handleTabChange() {
    // Only update the FAB's heroTag when tab changes
    // No need to call setState() here as it causes unnecessary rebuilds
    if (!_tabController.indexIsChanging) {
      // We'll use the current tab index in the build method
      // This avoids unnecessary rebuilds that can cause vibration
    }
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Add listener to update UI when tab changes
    _tabController.addListener(_handleTabChange);
    _loadCustomerSummary();

    // Listen for currency changes
    _currencySubscription = CurrencyService.onCurrencyChanged.listen((_) {
      // When currency changes, refresh the UI
      if (mounted) {
        setState(() {
          // Just trigger a rebuild to update currency formatting
        });
      }
    });

    // Listen for data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      // Refresh data when relevant changes occur
      if (changeType == DataChangeType.payment ||
          changeType == DataChangeType.bill ||
          changeType == DataChangeType.customer ||
          changeType == DataChangeType.all) {
        if (mounted) {
          debugPrint(
              'CustomerDetailScreen: Refreshing due to ${changeType.toString()} change');
          _refreshAllTabs();
          _loadCustomerSummary();
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _currencySubscription.cancel();
    _dataChangeSubscription.cancel();
    super.dispose();
  }

  // Custom method to handle PDF generation based on the active tab
  Future<void> _handlePdfGeneration(BuildContext context) async {
    // Determine which type of PDF to generate based on active tab
    // _tabController.index == 0 ? 'Transactions' : 'Payments'

    try {
      // Get the data based on the active tab
      if (_tabController.index == 0) {
        // Transactions tab
        // Get bills for this customer
        final bills = await DatabaseService.getBillsByCustomer(widget.customer.id);

        // Create PDF data for customer detail with transactions
        final pdfData = {
          'customerDetail': true,
          'customer': widget.customer,
          'bills': bills,
          'summary': {
            'totalBilled': _totalBilledAmount,
            'totalPaid': _totalPaidAmount,
            'netBalance': _netBalance,
          },
        };

        // No need to store context as we'll check mounted before using it

        // Check if widget is still mounted before proceeding
        if (!mounted) return;

        // Check if widget is still mounted before proceeding with PDF generation
        if (!mounted) return;

        // Store a local copy of the PDF data and settings
        final localPdfData = pdfData;
        final pdfSettings = PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': '${widget.customer.name} - Transaction Statement',
          },
        );

        // Capture the context before the async gap
        final capturedContext = context;

        // Use the universal PDF service to generate the PDF with modern design
        if (mounted) {
          // Create a local function to handle PDF generation
          Future<void> generatePdf() async {
            await UniversalPdfService.handlePdf(
              capturedContext,
              localPdfData,
              autoOpen: true,
              showSaveOption: true,
              showShareOption: true,
              pdfSettings: pdfSettings,
            );
          }

          // Call the local function
          await generatePdf();
        }
      } else {
        // Payments tab
        // Get payments for this customer
        final payments = await DatabaseService.getPaymentsByCustomer(widget.customer.id);

        // Create PDF data for payments
        final pdfData = {
          'payments': payments,
          'customer': widget.customer,
          'summary': {
            'totalAmount': payments.fold(0.0, (sum, payment) => sum + payment.amount),
            'count': payments.length,
          },
          'useModernTable': true, // Add this flag to use modern table
        };

        // Check if widget is still mounted before proceeding
        if (!mounted) return;

        // Store a local copy of the PDF data and settings
        final localPdfData = pdfData;
        final pdfSettings = PdfSettings.modernInvoice().copyWith(
          primaryColor: PdfColor.fromHex('#2E7D32'), // Green
          accentColor: PdfColor.fromHex('#1565C0'), // Blue
          additionalSettings: {
            'footerText': '${widget.customer.name} - Payment Report',
            'title': 'Payment Report',
          },
        );

        // Capture the context before the async gap
        final capturedContext = context;

        // Use the universal PDF service to generate the PDF
        if (mounted) {
          // Create a local function to handle PDF generation
          Future<void> generatePdf() async {
            await UniversalPdfService.handlePdf(
              capturedContext,
              localPdfData,
              autoOpen: true,
              showSaveOption: true,
              showShareOption: true,
              pdfSettings: pdfSettings,
            );
          }

          // Call the local function
          await generatePdf();
        }
      }
    } catch (e) {
      if (!mounted) return;

      // Show error message - we've already checked mounted above
      if (mounted) {
        // Create the error message
        final errorMessage = 'Error generating PDF: $e';

        // Capture the context before using it
        final capturedContext = context;

        // Show the error message
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(capturedContext).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCustomerSummary() async {
    try {
      setState(() {
        _isLoadingSummary = true;
      });

      // Use the optimized method to get all summary data at once
      final summaryData =
          await DatabaseService.getCustomerFinancialSummary(widget.customer.id);

      setState(() {
        _totalBilledAmount = summaryData['totalBilled'] as double;
        _totalPaidAmount = summaryData['totalPaid'] as double;
        _netBalance = summaryData['netBalance'] as double;
        _isLoadingSummary = false;
      });
    } catch (e) {
      // Error handled by setting isLoadingSummary to false
      setState(() {
        _isLoadingSummary = false;
      });
    }
  }

  // Make the loadCustomerSummary method public so tabs can call it
  Future<void> loadCustomerSummary() async {
    return _loadCustomerSummary();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TubewellAppBar(
        title: widget.customer.name,
        showBackButton: true,
        onPdfPressed: () => _handlePdfGeneration(context),
      ),
      body: Column(
        children: [
          // Tab bar with fixed height to prevent layout shifts
          PreferredSize(
            preferredSize: const Size.fromHeight(56.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(bottom: BorderSide(color: Colors.grey.shade300, width: 1)),
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Theme.of(context).primaryColor,
                indicatorWeight: 3.0,
                tabs: const [
                  Tab(text: 'TRANSACTIONS', icon: Icon(Icons.receipt_long)),
                  Tab(text: 'PAYMENTS', icon: Icon(Icons.payments)),
                ],
              ),
            ),
          ),

          // Customer summary card
          _buildCustomerSummaryCard(),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CustomerTransactionsTab(
                  key: _transactionsTabKey,
                  customer: widget.customer,
                  onDataChanged: loadCustomerSummary,
                ),
                CustomerPaymentsTab(
                  key: _paymentsTabKey,
                  customer: widget.customer,
                  onDataChanged: loadCustomerSummary,
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'fab-customer-detail-${_tabController.index}',
        backgroundColor: const Color(0xFF2E7D32),
        onPressed: () {
          if (_tabController.index == 0) {
            // Add transaction with smooth transition
            NavigationHelper.navigateWithSlide(
              context,
              TransactionFormScreen(
                selectedCustomer: widget.customer,
              ),
            ).then((result) {
              // Check if widget is still mounted
              if (!mounted) return;

              if (result == true) {
                // Force refresh on all tabs
                _refreshAllTabs();

                // Show success message - safe to use context as we've checked mounted
                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Transaction added successfully'),
                    backgroundColor: Color(0xFF2E7D32),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            });
          } else {
            // For the payments tab, navigate directly to the auto payment form
            NavigationHelper.navigateWithSlide(
              context,
              AutoPaymentFormScreen(
                customer: widget.customer,
              ),
            ).then((result) {
              // Check if widget is still mounted
              if (!mounted) return;

              if (result == true) {
                // Force refresh on all tabs
                _refreshAllTabs();

                // Show success message - safe to use context as we've checked mounted
                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Payment added successfully'),
                    backgroundColor: Color(0xFF2E7D32),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            });
          }
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildCustomerSummaryCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.teal.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.teal.shade200),
      ),
      child: _isLoadingSummary
          ? const Center(child: CircularProgressIndicator(strokeWidth: 2))
          : Column(
              children: [
                // Header with refresh button and reminder button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Customer Summary',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.teal.shade900,
                        fontSize: 18,
                      ),
                    ),
                    Row(
                      children: [
                        // Send reminder button
                        if (_netBalance <
                            0) // Only show for customers with outstanding balance
                          GestureDetector(
                            onTap: _showReminderDialog,
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              margin: const EdgeInsets.only(right: 8),
                              decoration: BoxDecoration(
                                color: Colors.purple.shade50,
                                borderRadius: BorderRadius.circular(16),
                                border:
                                    Border.all(color: Colors.purple.shade200),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.notifications_active,
                                      size: 16, color: Colors.purple.shade700),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Remind',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.purple.shade700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        // Refresh button
                        GestureDetector(
                          onTap: _loadCustomerSummary,
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Icon(Icons.refresh,
                                size: 16, color: Colors.blue.shade700),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Net Balance display with improved visual styling
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _netBalance >= 0
                        ? Colors.green.shade50
                        : Colors.red.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                        color: _netBalance >= 0
                            ? Colors.green.shade200
                            : Colors.red.shade200),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _netBalance >= 0
                                ? Icons.check_circle
                                : Icons.warning,
                            color: _netBalance >= 0
                                ? Colors.green.shade700
                                : Colors.red.shade700,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            _netBalance >= 0
                                ? 'Remaining Credit'
                                : 'Outstanding Balance',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: _netBalance >= 0
                                  ? Colors.green.shade800
                                  : Colors.red.shade800,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            CurrencyService.formatCurrency(_netBalance.abs()),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 22,
                              color: _netBalance >= 0
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                Row(
                  children: [
                    Expanded(
                      child: _buildFinancialSummaryItem(
                        title: 'Total Billed',
                        value:
                            CurrencyService.formatCurrency(_totalBilledAmount),
                        icon: Icons.receipt_long,
                        iconColor: Colors.indigo.shade700,
                        bgColor: Colors.indigo.shade50,
                        borderColor: Colors.indigo.shade200,
                        textColor: Colors.indigo.shade800,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildFinancialSummaryItem(
                        title: 'Total Paid',
                        value: CurrencyService.formatCurrency(_totalPaidAmount),
                        icon: Icons.paid,
                        iconColor: Colors.green.shade700,
                        bgColor: Colors.green.shade50,
                        borderColor: Colors.green.shade200,
                        textColor: Colors.green.shade800,
                      ),
                    ),
                  ],
                ),
              ],
            ),
    );
  }

  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color textColor,
    required Color borderColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  color: textColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: textColor,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  // Method to refresh all data in the details screen
  void _refreshAllTabs() {
    // Refresh the summary data
    _loadCustomerSummary();

    // Directly access and refresh the tab states using the global keys
    _transactionsTabKey.currentState?.refreshData();
    _paymentsTabKey.currentState?.refreshData();

    // Force UI update
    setState(() {});
  }

  // Show reminder dialog for all unpaid bills
  void _showReminderDialog() async {
    try {
      // Show loading indicator
      setState(() {
        _isLoadingSummary = true;
      });

      // Get all unpaid bills for this customer
      final bills =
          await DatabaseService.getBillsByCustomer(widget.customer.id);
      final unpaidBills = bills.where((bill) => !bill.isPaid).toList();

      setState(() {
        _isLoadingSummary = false;
      });

      if (unpaidBills.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No unpaid bills to send reminders for'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => ReminderDialog(
            customer: widget.customer,
            bills: unpaidBills,
            title: 'Send Payment Reminder',
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoadingSummary = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading unpaid bills: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class CustomerTransactionsTab extends StatefulWidget {
  final Customer customer;
  final VoidCallback onDataChanged;

  const CustomerTransactionsTab({
    super.key,
    required this.customer,
    required this.onDataChanged,
  });

  @override
  State<CustomerTransactionsTab> createState() =>
      _CustomerTransactionsTabState();
}

class _CustomerTransactionsTabState extends State<CustomerTransactionsTab>
    with AutomaticKeepAliveClientMixin {
  List<Bill> _bills = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();

  // Group bills by date
  Map<String, List<Bill>> _groupedBills = {};
  List<String> _dateKeys = [];

  // For pull-to-refresh coordination
  Completer<void>? _refreshCompleter;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadBills();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Only refresh if not already loading
    if (!_isLoading && !_isLoadingMore) {
      _loadBills();
    }
  }

  // Scroll listener for infinite scrolling
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMoreBills();
      }
    }
  }

  // Public method to force a refresh from parent
  void refreshData() {
    if (mounted && !_isLoading) {
      _loadBills();
    }
  }

  // Used by pull-to-refresh
  Future<void> _handleRefresh() {
    _refreshCompleter = Completer<void>();
    refreshData();
    return _refreshCompleter!.future;
  }

  Future<void> _loadBills() async {
    if (!mounted) return;

    // Reset pagination
    _page = 0;
    _hasMoreData = true;

    setState(() {
      _isLoading = true;
    });

    try {
      final bills = await DatabaseService.getBillsByCustomerPaginated(
          widget.customer.id,
          page: _page,
          pageSize: _pageSize);

      if (!mounted) return;
      _processLoadedBills(bills, isFirstPage: true);
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading transactions: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }

    // Complete the refresh indicator if it's active
    _completeRefresh();
  }

  Future<void> _loadMoreBills() async {
    if (!mounted || !_hasMoreData || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _page++;
      final bills = await DatabaseService.getBillsByCustomerPaginated(
          widget.customer.id,
          page: _page,
          pageSize: _pageSize);

      if (!mounted) return;
      _processLoadedBills(bills, isFirstPage: false);
    } catch (e) {
      if (!mounted) return;
      // Revert page increment on error
      _page--;

      setState(() {
        _isLoadingMore = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading more transactions: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _processLoadedBills(List<Bill> bills, {required bool isFirstPage}) {
    if (!mounted) return;

    // Check if we've reached the end
    _hasMoreData = bills.length >= _pageSize;

    // Update the bills list
    if (isFirstPage) {
      _bills = bills;
    } else {
      _bills.addAll(bills);
    }

    // Sort bills by date (newest first)
    _bills.sort((a, b) => b.billDate.compareTo(a.billDate));

    // Group bills by date
    _groupedBills = {};
    for (var bill in _bills) {
      final dateKey = DateFormat('yyyy-MM-dd').format(bill.billDate);
      if (!_groupedBills.containsKey(dateKey)) {
        _groupedBills[dateKey] = [];
      }
      _groupedBills[dateKey]!.add(bill);
    }

    // Sort date keys (newest first)
    _dateKeys = _groupedBills.keys.toList()..sort((a, b) => b.compareTo(a));

    setState(() {
      _isLoading = false;
      _isLoadingMore = false;
    });

    // Notify parent to refresh summary
    widget.onDataChanged();

    // Complete the refresh indicator if it's active
    _completeRefresh();
  }

  void _completeRefresh() {
    if (_refreshCompleter?.isCompleted == false) {
      _refreshCompleter?.complete();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    if (_isLoading && _bills.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_bills.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.receipt_long,
        title: 'No Transactions',
        message: 'This customer has no transactions yet.',
        buttonText: 'Add Transaction',
        onButtonPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TransactionFormScreen(
                selectedCustomer: widget.customer,
              ),
            ),
          ).then((result) {
            if (!mounted) return;
            if (result == true) {
              _loadBills();
            }
          });
        },
      );
    }

    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _dateKeys.length + (_hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the end
          if (index == _dateKeys.length) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: _isLoadingMore
                    ? const CircularProgressIndicator(strokeWidth: 2)
                    : TextButton(
                        onPressed: _loadMoreBills,
                        child: Text('Load More',
                            style: TextStyle(color: Colors.teal.shade700)),
                      ),
              ),
            );
          }

          final dateKey = _dateKeys[index];
          final dateFormatted =
              DateFormat('EEEE, dd MMMM yyyy').format(DateTime.parse(dateKey));
          final billsForDate = _groupedBills[dateKey] ?? [];

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date header
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.teal.shade700,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today,
                          color: Colors.white, size: 18),
                      const SizedBox(width: 8),
                      Text(
                        dateFormatted,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${billsForDate.length} ${billsForDate.length == 1 ? 'transaction' : 'transactions'}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Bills for this date
              ...billsForDate.map((bill) {
                final billId = '#${bill.id.toString().padLeft(3, '0')}';
                return Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Card(
                    elevation: 3,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: bill.isPaid
                            ? Colors.green.shade400
                            : bill.isPartiallyPaid
                                ? Colors.blue.shade400
                                : Colors.red.shade400,
                        width: 2.0,
                      ),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BillDetailsScreen(
                              bill: bill,
                              customer: widget.customer,
                            ),
                          ),
                        ).then((_) {
                          refreshData();
                        });
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Row 1: Bill ID and Payment Status
                            Row(
                              children: [
                                // Bill ID with icon
                                Expanded(
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.indigo.shade50,
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(
                                          color: Colors.indigo.shade200),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.receipt,
                                            size: 14,
                                            color: Colors.indigo.shade700),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            "Bill No $billId",
                                            style: TextStyle(
                                              color: Colors.indigo.shade800,
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(width: 8),

                                // Status chip
                                Expanded(
                                  child: StatusChip(
                                    status: bill.isPaid
                                        ? 'PAID'
                                        : bill.isPartiallyPaid
                                            ? 'PARTIAL'
                                            : 'UNPAID',
                                    isPaid: bill.isPaid,
                                    isPartiallyPaid: bill.isPartiallyPaid,
                                    fontSize: 12,
                                  ),
                                ),

                                const SizedBox(width: 8),

                                // Edit button
                                GestureDetector(
                                  onTap: () {
                                    NavigationHelper.navigateWithSlide(
                                      context,
                                      TransactionFormScreen(
                                        existingBill: bill,
                                        selectedCustomer: widget.customer,
                                      ),
                                    ).then((result) {
                                      if (result == true) {
                                        refreshData();
                                      }
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(5),
                                    decoration: BoxDecoration(
                                      color: Colors.cyan.shade50,
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(
                                          color: Colors.cyan.shade200),
                                    ),
                                    child: Icon(Icons.edit,
                                        color: Colors.cyan.shade700, size: 14),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 12),
                            const Divider(height: 1, thickness: 1),
                            const SizedBox(height: 12),

                            // Row 2: Duration and Amount
                            Row(
                              children: [
                                // Time duration with icon
                                Expanded(
                                  child: DurationChip(
                                    hours: bill.durationHoursWhole,
                                    minutes: bill.durationMinutes,
                                    showLabel: false,
                                  ),
                                ),

                                const SizedBox(width: 8),

                                // Amount with icon
                                Expanded(
                                  child: AmountChip(
                                    amount: bill.amount,
                                    showLabel: false,
                                    customColor: bill.isPaid
                                        ? Colors.green.shade700
                                        : bill.isPartiallyPaid
                                            ? Colors.blue.shade700
                                            : Colors.red.shade700,
                                  ),
                                ),

                                const SizedBox(width: 8),

                                // Delete button
                                GestureDetector(
                                  onTap: () => _showDeleteConfirmation(bill),
                                  child: Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: Colors.red.shade200),
                                    ),
                                    child: Icon(Icons.delete,
                                        color: Colors.red.shade700, size: 16),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ],
          );
        },
      ),
    );
  }

  void _showDeleteConfirmation(Bill bill) async {
    // Check if the bill has linked payments
    final payments = await DatabaseService.getPaymentsByBill(bill.id);

    // Check if widget is still mounted after async operation
    if (!mounted) return;

    final hasLinkedPayments = payments.isNotEmpty;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title:
            Text('Delete Bill', style: TextStyle(color: Colors.red.shade700)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to delete this bill?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // Bill details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Bill #${bill.id}'),
                  Text('Amount: Rs. ${bill.amount.toStringAsFixed(2)}'),
                  Text(
                      'Duration: ${bill.durationHoursWhole}h ${bill.durationMinutes}m'),
                  Text(
                      'Status: ${bill.isPaid ? 'Paid' : (bill.isPartiallyPaid ? 'Partially Paid' : 'Unpaid')}'),
                ],
              ),
            ),

            if (hasLinkedPayments) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'This bill has ${payments.length} linked payment(s)',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade800,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Deleting this bill will convert these payments to customer credit.',
                      style: TextStyle(color: Colors.orange.shade900),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => NavigationHelper.goBack(context),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              NavigationHelper.goBack(context);
              _deleteBill(bill);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteBill(Bill bill) async {
    try {
      // Use the BillingService to safely delete the bill and handle linked payments
      final success = await BillingService.deleteBillSafely(bill.id);

      // Check if widget is still mounted after async operation
      if (!mounted) return;

      if (!success) {
        throw Exception('Failed to delete bill');
      }

      // Refresh the transactions list
      refreshData();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bill deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Check if widget is still mounted
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete bill: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

class CustomerPaymentsTab extends StatefulWidget {
  final Customer customer;
  final VoidCallback onDataChanged;

  const CustomerPaymentsTab({
    super.key,
    required this.customer,
    required this.onDataChanged,
  });

  @override
  State<CustomerPaymentsTab> createState() => _CustomerPaymentsTabState();
}

class _CustomerPaymentsTabState extends State<CustomerPaymentsTab>
    with AutomaticKeepAliveClientMixin {
  List<Payment> _payments = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();

  // For pull-to-refresh coordination
  Completer<void>? _refreshCompleter;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadPayments();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Only refresh if not already loading
    if (!_isLoading && !_isLoadingMore) {
      _loadPayments();
    }
  }

  // Scroll listener for infinite scrolling
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMorePayments();
      }
    }
  }

  // Public method to force a refresh from parent
  void refreshData() {
    if (mounted && !_isLoading) {
      _loadPayments();
    }
  }

  // Used by pull-to-refresh
  Future<void> _handleRefresh() {
    _refreshCompleter = Completer<void>();
    refreshData();
    return _refreshCompleter!.future;
  }

  Future<void> _loadPayments() async {
    if (!mounted) return;

    // Reset pagination
    _page = 0;
    _hasMoreData = true;

    setState(() {
      _isLoading = true;
    });

    try {
      final payments = await DatabaseService.getPaymentsByCustomerPaginated(
          widget.customer.id,
          page: _page,
          pageSize: _pageSize);

      _processLoadedPayments(payments, isFirstPage: true);
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading payments: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Complete the refresh indicator if it's active
      _completeRefresh();
    }
  }

  Future<void> _loadMorePayments() async {
    if (!mounted || !_hasMoreData || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _page++;
      final payments = await DatabaseService.getPaymentsByCustomerPaginated(
          widget.customer.id,
          page: _page,
          pageSize: _pageSize);

      _processLoadedPayments(payments, isFirstPage: false);
    } catch (e) {
      if (mounted) {
        // Revert page increment on error
        _page--;

        setState(() {
          _isLoadingMore = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading more payments: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _processLoadedPayments(List<Payment> payments,
      {required bool isFirstPage}) {
    if (!mounted) return;

    // Check if we've reached the end
    _hasMoreData = payments.length >= _pageSize;

    // Update the payments list
    if (isFirstPage) {
      _payments = payments;
    } else {
      _payments.addAll(payments);
    }

    // Sort payments by date (newest first)
    _payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));

    setState(() {
      _isLoading = false;
      _isLoadingMore = false;
    });

    // Notify parent to refresh summary
    widget.onDataChanged();

    // Complete the refresh indicator if it's active
    _completeRefresh();
  }

  void _completeRefresh() {
    if (_refreshCompleter?.isCompleted == false) {
      _refreshCompleter?.complete();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    if (_isLoading && _payments.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_payments.isEmpty) {
      return SingleChildScrollView(
        child: Column(
          children: [
            EmptyStateWidget(
              icon: Icons.payment,
              title: 'No Payments',
              message: 'This customer has no payments yet.',
              buttonText: 'Add Payment',
              onButtonPressed: () {
                // Navigate directly to auto payment form
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AutoPaymentFormScreen(
                      customer: widget.customer,
                    ),
                  ),
                ).then((result) {
                  if (result == true) {
                    refreshData();
                  }
                });
              },
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: Column(
        children: [
          // Payment list
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _payments.length + (_hasMoreData ? 1 : 0),
              itemBuilder: (context, index) {
                // Show loading indicator at the end
                if (index == _payments.length) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Center(
                      child: _isLoadingMore
                          ? const CircularProgressIndicator(strokeWidth: 2)
                          : TextButton(
                              onPressed: _loadMorePayments,
                              child: Text('Load More',
                                  style:
                                      TextStyle(color: Colors.teal.shade700)),
                            ),
                    ),
                  );
                }

                final payment = _payments[index];
                return _buildPaymentCard(payment, context);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentCard(Payment payment, BuildContext context) {
    final formattedDate = DateFormat('dd MMM yyyy').format(payment.paymentDate);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: payment.billId > 0
                ? Colors.green.shade200
                : Colors.blue.shade200,
            width: 1.5,
          ),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PaymentsScreen(
                  selectedCustomer: widget.customer,
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Payment ID and Bill allocation status
                Row(
                  children: [
                    // Payment ID with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.indigo.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.indigo.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.payments,
                                size: 16, color: Colors.indigo.shade700),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                "Payment No #${payment.id.toString().padLeft(2, '0')}",
                                style: TextStyle(
                                  color: Colors.indigo.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Bill allocation status chip
                    Expanded(
                      child: FutureBuilder<List<int>>(
                        future: _getLinkedBillIds(payment),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.blue.shade200),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.hourglass_empty,
                                      size: 16, color: Colors.blue.shade700),
                                  const SizedBox(width: 6),
                                  Expanded(
                                    child: Text(
                                      "Loading...",
                                      style: TextStyle(
                                        color: Colors.blue.shade800,
                                        fontSize: 13,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          final billIds = snapshot.data ?? [];
                          IconData icon;
                          String text;
                          Color bgColor;
                          Color borderColor;
                          Color iconColor;
                          Color textColor;

                          if (billIds.isEmpty) {
                            icon = Icons.account_balance_wallet;
                            text = 'Credit';
                            bgColor = Colors.teal.shade50;
                            borderColor = Colors.teal.shade200;
                            iconColor = Colors.teal.shade700;
                            textColor = Colors.teal.shade800;
                          } else if (billIds.length == 1) {
                            icon = Icons.receipt;
                            text = 'Bill No #${billIds[0]}';
                            bgColor = Colors.blue.shade50;
                            borderColor = Colors.blue.shade200;
                            iconColor = Colors.blue.shade700;
                            textColor = Colors.blue.shade800;
                          } else {
                            icon = Icons.receipt_long;
                            text = '${billIds.length} Bills';
                            bgColor = Colors.blue.shade50;
                            borderColor = Colors.blue.shade200;
                            iconColor = Colors.blue.shade700;
                            textColor = Colors.blue.shade800;
                          }

                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: bgColor,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: borderColor),
                            ),
                            child: Row(
                              children: [
                                Icon(icon, size: 16, color: iconColor),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    text,
                                    style: TextStyle(
                                      color: textColor,
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AutoPaymentFormScreen(
                              customer: widget.customer,
                              existingPayment: payment,
                            ),
                          ),
                        ).then((result) {
                          if (result == true) {
                            refreshData();
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.cyan.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.cyan.shade200),
                        ),
                        child: Icon(Icons.edit,
                            color: Colors.cyan.shade700, size: 16),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),
                const Divider(height: 1, thickness: 1),
                const SizedBox(height: 12),

                // Row 2: Date, amount and delete
                Row(
                  children: [
                    // Date with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 12, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Amount with icon
                    Expanded(
                      child: AmountChip(
                        amount: payment.amount,
                        showLabel: false,
                        customColor: payment.billId > 0
                            ? Colors.green.shade700
                            : Colors.indigo.shade700,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    GestureDetector(
                      onTap: () => _confirmDeletePayment(context, payment),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Icon(Icons.delete,
                            color: Colors.red.shade700, size: 16),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _confirmDeletePayment(BuildContext context, Payment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.warning,
                        color: Colors.orange.shade700, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'This action cannot be undone.',
                        style: TextStyle(
                          color: Colors.orange.shade900,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text('Payment details:'),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.tag, color: Colors.blue.shade700, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Payment ID: ${payment.id}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.payments,
                            color: Colors.indigo.shade700, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Amount: Rs ${payment.amount.toStringAsFixed(2)}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            color: Colors.purple.shade700, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Date: ${DateFormat('dd MMM yyyy').format(payment.paymentDate)}',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.delete_forever, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'This action cannot be undone.',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deletePayment(payment);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  void _deletePayment(Payment payment) async {
    try {
      // Optimistic UI update - remove from list immediately
      setState(() {
        _payments.remove(payment);
      });

      // Use PaymentService instead of DatabaseService to properly handle bill status updates
      await PaymentService.deletePaymentAndUpdateBillStatus(payment);

      // Refresh data to ensure consistency
      refreshData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Payment deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Refresh data to restore correct state in case of error
        refreshData();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete payment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Extract linked bill IDs from a payment
  Future<List<int>> _getLinkedBillIds(Payment payment) async {
    final List<int> billIds = [];

    // Add the primary billId if it's valid
    if (payment.billId > 0) {
      billIds.add(payment.billId);
    }

    // Get payment allocations from database service
    try {
      final allocations =
          await DatabaseService.getPaymentAllocationsByPaymentId(payment.id);
      for (var allocation in allocations) {
        if (allocation.billId > 0) {
          final billId = allocation.billId;
          if (!billIds.contains(billId)) {
            billIds.add(billId);
          }
        }
      }
    } catch (e) {
      // Error handled silently
    }

    // Extract additional bill IDs from remarks
    final remarks = payment.remarks?.toLowerCase() ?? '';

    // Look for patterns like "Rs X for bill #Y"
    RegExp billRegex = RegExp(r'bill #(\d+)|bill (\d+)|bill.*?(\d+)');
    final matches = billRegex.allMatches(remarks);

    for (final match in matches) {
      // Try to get the bill ID from any capturing group
      String? billIdStr;
      for (int i = 1; i <= match.groupCount; i++) {
        if (match.group(i) != null) {
          billIdStr = match.group(i);
          break;
        }
      }

      if (billIdStr != null) {
        final billId = int.tryParse(billIdStr);
        if (billId != null && billId > 0 && !billIds.contains(billId)) {
          billIds.add(billId);
        }
      }
    }

    return billIds;
  }
}
