import 'package:tubewell_water_billing_app/models/payment.dart';

class Bill {
  int id;
  int customerId;
  DateTime billDate;
  DateTime startTime;
  DateTime endTime;
  double durationHours;
  int durationHoursWhole;
  int durationMinutes;
  double hourlyRate;
  double amount;

  // Discount fields
  double? discountAmount;
  double? discountTime; // in minutes

  String? remarks;

  // Payment status fields
  bool isPaid = false;
  bool isPartiallyPaid = false;
  double? partialAmount;
  DateTime? paidDate;

  // Payment tracking fields
  double _paidAmount = 0;

  // Getter for outstanding amount
  double get outstandingAmount {
    if (isPaid) return 0;
    return amount - (partialAmount ?? 0);
  }

  // Getter for ratePerHour (alias for hourlyRate)
  double get ratePerHour => hourlyRate;

  // Method to update payment status based on payments
  void updatePaymentStatus(List<Payment> payments) {
    // Updating payment status for bill #$id with ${payments.length} payments

    // Calculate total paid amount from payments with safer handling of nulls
    double paidAmount = 0.0;

    // First, check if any payment is directly linked to this bill via billId
    for (final payment in payments) {
      // Ensure we're adding a valid value
      // Check if this payment is directly linked to this bill
      if (payment.billId == id) {
        // This payment is directly linked to this bill, add the full amount
        paidAmount += payment.amount;
        // Adding full amount ${payment.amount} from payment #${payment.id} (direct link)
      }
    }

    // If no direct payments were found, check for payments linked through allocations
    if (paidAmount == 0.0) {
      // In a real implementation, we would check PaymentAllocation records
      // For now, we'll distribute payments evenly across unpaid bills
      // This is a simplified approach for demonstration purposes

      // Calculate the total amount needed for this bill
      double remainingAmount = amount;

      // Sort payments by date (oldest first)
      payments.sort((a, b) => a.paymentDate.compareTo(b.paymentDate));

      // Allocate payments to this bill
      for (final payment in payments) {
        if (payment.amount > 0 && remainingAmount > 0) {
          // Determine how much to allocate from this payment
          double allocationAmount = payment.amount > remainingAmount
              ? remainingAmount
              : payment.amount;

          // Add the allocation amount to the paid amount
          paidAmount += allocationAmount;
          // Adding allocation amount $allocationAmount from payment #${payment.id} (via allocation)

          // Reduce the remaining amount needed
          remainingAmount -= allocationAmount;
        }
      }
    }

    _paidAmount = paidAmount;

    // Bill #$id: Total paid amount: $_paidAmount, Bill amount: $amount

    // Update payment status based on the paid amount
    if (_paidAmount >= amount) {
      isPaid = true;
      isPartiallyPaid = false;
      partialAmount = null;
      // Only set paidDate if not already set
      if (paidDate == null && payments.isNotEmpty) {
        // Use the most recent payment date
        final latestPayment = payments
            .reduce((a, b) => a.paymentDate.isAfter(b.paymentDate) ? a : b);
        paidDate = latestPayment.paymentDate;
      }
      // Bill #$id marked as PAID
    } else if (_paidAmount > 0) {
      isPaid = false;
      isPartiallyPaid = true;
      partialAmount = _paidAmount;
      // Bill #$id marked as PARTIALLY PAID (amount: $_paidAmount)
    } else {
      isPaid = false;
      isPartiallyPaid = false;
      partialAmount = null;
      paidDate = null;
      // Bill #$id marked as UNPAID
    }
  }

  // Named constructor with required parameters
  Bill.create({
    required this.customerId,
    required this.startTime,
    required this.endTime,
    DateTime? billDate,
    this.hourlyRate = 900.0,
    this.remarks,
    this.discountAmount,
    this.discountTime,
  })  : id = 0,
        billDate = billDate ?? DateTime.now(),
        durationHours = 0,
        durationHoursWhole = 0,
        durationMinutes = 0,
        amount = 0 {
    isPaid = false;
    isPartiallyPaid = false;
    partialAmount = null;
    _paidAmount = 0;
    calculateBill();
  }

  // Default constructor
  Bill({
    this.id = 0,
    required this.customerId,
    required this.billDate,
    required this.startTime,
    required this.endTime,
    required this.durationHours,
    required this.durationHoursWhole,
    required this.durationMinutes,
    required this.hourlyRate,
    required this.amount,
    this.discountAmount,
    this.discountTime,
    this.remarks,
    this.isPaid = false,
    this.isPartiallyPaid = false,
    this.partialAmount,
    this.paidDate,
  }) : _paidAmount = 0;

  // Convert a Bill object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'customerId': customerId,
      'billDate': billDate.toIso8601String(),
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'durationHours': durationHours,
      'durationHoursWhole': durationHoursWhole,
      'durationMinutes': durationMinutes,
      'hourlyRate': hourlyRate,
      'amount': amount,
      'discountAmount': discountAmount,
      'discountTime': discountTime,
      'remarks': remarks,
      'isPaid': isPaid ? 1 : 0,
      'isPartiallyPaid': isPartiallyPaid ? 1 : 0,
      'partialAmount': partialAmount,
      'paidDate': paidDate?.toIso8601String(),
      'paidAmount': _paidAmount,
    };
  }

  // Create a Bill object from a Map
  factory Bill.fromMap(Map<String, dynamic> map) {
    return Bill(
      id: map['id'],
      customerId: map['customerId'],
      billDate: DateTime.parse(map['billDate']),
      startTime: DateTime.parse(map['startTime']),
      endTime: DateTime.parse(map['endTime']),
      durationHours: map['durationHours'],
      durationHoursWhole: map['durationHoursWhole'],
      durationMinutes: map['durationMinutes'],
      hourlyRate: map['hourlyRate'],
      amount: map['amount'],
      discountAmount: map['discountAmount'],
      discountTime: map['discountTime'],
      remarks: map['remarks'],
      isPaid: map['isPaid'] == 1,
      isPartiallyPaid: map['isPartiallyPaid'] == 1,
      partialAmount: map['partialAmount'],
      paidDate:
          map['paidDate'] != null ? DateTime.parse(map['paidDate']) : null,
    )..setPaidAmount(map['paidAmount'] ?? 0.0);
  }

  // Setter for _paidAmount
  void setPaidAmount(double amount) {
    _paidAmount = amount;
  }

  void calculateBill() {
    final difference = endTime.difference(startTime);

    // Apply time discount if available
    int discountedMinutes = difference.inMinutes;
    if (discountTime != null && discountTime! > 0) {
      discountedMinutes = (difference.inMinutes - discountTime!).round();
      if (discountedMinutes < 0) discountedMinutes = 0;
    }

    durationHours = discountedMinutes / 60.0;

    // Calculate hours and minutes components
    durationHoursWhole = discountedMinutes ~/ 60;
    durationMinutes = discountedMinutes % 60;

    // Calculate amount with hourly rate
    amount = durationHours * hourlyRate;

    // Apply amount discount if available
    if (discountAmount != null && discountAmount! > 0) {
      amount = amount - discountAmount!;
      if (amount < 0) amount = 0;
    }
  }

  // Helper method to get formatted duration string
  String getFormattedDuration() {
    return '$durationHoursWhole hours $durationMinutes minutes';
  }

  // Clone method to create a copy of this bill
  Bill clone() {
    return Bill(
      id: id,
      customerId: customerId,
      billDate: billDate,
      startTime: startTime,
      endTime: endTime,
      durationHours: durationHours,
      durationHoursWhole: durationHoursWhole,
      durationMinutes: durationMinutes,
      hourlyRate: hourlyRate,
      amount: amount,
      remarks: remarks,
      isPaid: isPaid,
      isPartiallyPaid: isPartiallyPaid,
      partialAmount: partialAmount,
      discountAmount: discountAmount,
      discountTime: discountTime,
      paidDate: paidDate,
    )..setPaidAmount(_paidAmount);
  }
}
