import 'dart:io';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';

/// Base service for PDF generation with common styling and utility functions
class PdfBaseService {
  // Logger instance for logging errors
  static final Logger _logger = Logger();
  // Modern color palette
  // Primary colors
  static final PdfColor primaryColor = PdfColor.fromHex('#2563EB'); // Modern blue
  static final PdfColor accentColor = PdfColor.fromHex('#10B981'); // Modern green
  static final PdfColor secondaryColor = PdfColor.fromHex('#0891B2'); // Modern teal

  // Text colors
  static final PdfColor textColor = PdfColor.fromHex('#1E293B'); // Slate 800
  static final PdfColor lightTextColor = PdfColor.fromHex('#64748B'); // Slate 500

  // Background colors
  static final PdfColor lightBlue = PdfColor.fromHex('#EFF6FF'); // Blue 50
  static final PdfColor lightGreen = PdfColor.fromHex('#ECFDF5'); // Green 50
  static final PdfColor lightRed = PdfColor.fromHex('#FEF2F2'); // Red 50
  static final PdfColor lightOrange = PdfColor.fromHex('#FFF7ED'); // Orange 50
  static final PdfColor lightPurple = PdfColor.fromHex('#F5F3FF'); // Purple 50
  static final PdfColor lightTeal = PdfColor.fromHex('#ECFEFF'); // Cyan 50

  // Border and divider colors
  static final PdfColor borderColor = PdfColor.fromHex('#E2E8F0'); // Slate 200
  static final PdfColor dividerColor = PdfColor.fromHex('#CBD5E1'); // Slate 300

  // Status colors
  static final PdfColor paidColor = PdfColor.fromHex('#10B981'); // Modern green
  static final PdfColor partialColor = PdfColor.fromHex('#F59E0B'); // Modern amber
  static final PdfColor unpaidColor = PdfColor.fromHex('#EF4444'); // Modern red
  static final PdfColor creditColor = PdfColor.fromHex('#8B5CF6'); // Modern purple
  static final PdfColor purpleColor = PdfColor.fromHex('#8B5CF6'); // Modern purple
  static final PdfColor amberColor = PdfColor.fromHex('#F59E0B'); // Modern amber

  // Common fonts - will be replaced with Google Fonts when loaded
  static pw.Font regularFont = pw.Font.helvetica();
  static pw.Font boldFont = pw.Font.helveticaBold();
  static pw.Font italicFont = pw.Font.helveticaOblique();

  /// Load Google Fonts for PDF
  static Future<void> loadFonts() async {
    try {
      // Try to load Google Fonts with proper error handling
      final fontFutures = await Future.wait([
        PdfGoogleFonts.nunitoRegular().catchError((e) {
          _logger.w('Error loading Nunito Regular: $e');
          return pw.Font.courier();
        }),
        PdfGoogleFonts.nunitoBold().catchError((e) {
          _logger.w('Error loading Nunito Bold: $e');
          return pw.Font.courierBold();
        }),
        PdfGoogleFonts.nunitoItalic().catchError((e) {
          _logger.w('Error loading Nunito Italic: $e');
          return pw.Font.courierOblique();
        }),
      ], eagerError: false);

      // Assign the loaded fonts
      regularFont = fontFutures[0];
      boldFont = fontFutures[1];
      italicFont = fontFutures[2];

      // Verify that all fonts were loaded successfully
      // No need to check type as fontFutures is already typed as List<pw.Font>
    } catch (e) {
      _logger.e('Error loading fonts: $e');
      // Fallback to Courier fonts which have better Unicode support than Helvetica
      regularFont = pw.Font.courier();
      boldFont = pw.Font.courierBold();
      italicFont = pw.Font.courierOblique();
    }
  }

  /// Create a standard header for PDF documents
  static pw.Widget buildHeader({
    required String title,
    String? subtitle,
    pw.ImageProvider? logo,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: lightBlue,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                title,
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 20,
                  color: primaryColor,
                ),
              ),
              if (subtitle != null)
                pw.Text(
                  subtitle,
                  style: pw.TextStyle(
                    font: italicFont,
                    fontSize: 14,
                    color: PdfColor.fromHex('#666666'),
                  ),
                ),
            ],
          ),
          if (logo != null)
            pw.Container(
              height: 50,
              width: 50,
              child: pw.Image(logo),
            ),
        ],
      ),
    );
  }

  /// Create a standard footer for PDF documents
  static pw.Widget buildFooter({
    String? customText,
  }) {
    final now = DateTime.now();
    final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(now);

    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: borderColor, width: 0.5)),
      ),
      padding: const pw.EdgeInsets.only(top: 8),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'Generated: $formattedDate',
            style: pw.TextStyle(font: italicFont, fontSize: 9),
          ),
          pw.Text(
            customText ?? 'Tubewell Water Billing System',
            style: pw.TextStyle(font: italicFont, fontSize: 9),
          ),
        ],
      ),
    );
  }

  /// Create a section title
  static pw.Widget buildSectionTitle(String title) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(top: 10, bottom: 5),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title.toUpperCase(),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 14,
              color: primaryColor,
            ),
          ),
          pw.Divider(color: primaryColor),
        ],
      ),
    );
  }

  /// Create a standard table with header
  static pw.Widget buildTable({
    required List<String> headers,
    required List<List<String>> data,
    List<double>? columnWidths,
  }) {
    // Calculate column widths if not provided
    final effectiveColumnWidths = columnWidths ??
        List.generate(headers.length, (_) => 1.0 / headers.length);

    // Create column width map
    final Map<int, pw.TableColumnWidth> columnWidthMap = {};
    for (int i = 0; i < effectiveColumnWidths.length; i++) {
      columnWidthMap[i] = pw.FlexColumnWidth(effectiveColumnWidths[i]);
    }

    return pw.Table(
      border: pw.TableBorder.all(
        color: borderColor,
        width: 0.5,
      ),
      columnWidths: columnWidthMap,
      children: [
        // Header row
        pw.TableRow(
          decoration: pw.BoxDecoration(color: lightBlue),
          children: headers.map((header) =>
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Text(
                header,
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 10,
                  color: primaryColor,
                ),
                textAlign: pw.TextAlign.center,
              ),
            )
          ).toList(),
        ),
        // Data rows
        ...data.map((row) =>
          pw.TableRow(
            children: row.map((cell) =>
              pw.Padding(
                padding: const pw.EdgeInsets.all(5),
                child: pw.Text(
                  cell,
                  style: pw.TextStyle(
                    font: regularFont,
                    fontSize: 9,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              )
            ).toList(),
          )
        ),
      ],
    );
  }

  /// Save PDF document to a file
  static Future<File> savePdf(pw.Document pdf, String fileName) async {
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/$fileName');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// Helper method to create a color with opacity
  static PdfColor colorWithOpacity(PdfColor color, double opacity) {
    return PdfColor(
      color.red,
      color.green,
      color.blue,
      opacity,
    );
  }

  /// Create a standard PDF document with modern styling
  static pw.Document createDocument() {
    return pw.Document(
      theme: pw.ThemeData.withFont(
        base: regularFont,
        bold: boldFont,
        italic: italicFont,
      ),
    );
  }
}
