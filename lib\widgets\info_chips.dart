import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';

enum ChipColorScheme {
  green,
  blue,
  teal,
  purple,
  indigo,
  red,
  custom,
}

/// A reusable styled information chip with consistent appearance
class StyledInfoChip extends StatelessWidget {
  final IconData icon;
  final String text;
  final ChipColorScheme colorScheme;
  final VoidCallback? onTap;
  final double? maxWidth;
  final double fontSize;
  final bool fontBold;
  final Color? customBackgroundColor;
  final Color? customBorderColor;
  final Color? customIconColor;
  final Color? customTextColor;

  const StyledInfoChip({
    super.key,
    required this.icon,
    required this.text,
    required this.colorScheme,
    this.onTap,
    this.maxWidth,
    this.fontSize = 12,
    this.fontBold = true,
    this.customBackgroundColor,
    this.customBorderColor,
    this.customIconColor,
    this.customTextColor,
  });

  @override
  Widget build(BuildContext context) {
    var colors = _getColors(colorScheme);

    // Override with custom colors if provided
    if (colorScheme == ChipColorScheme.custom) {
      colors = _ChipColors(
        backgroundColor: customBackgroundColor ?? colors.backgroundColor,
        borderColor: customBorderColor ?? colors.borderColor,
        iconColor: customIconColor ?? colors.iconColor,
        textColor: customTextColor ?? colors.textColor,
      );
    }

    // Use LayoutBuilder to make chip responsive to available width
    return LayoutBuilder(
      builder: (context, constraints) {
        final chip = Container(
          // Use constraints to adapt to available space instead of fixed width
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? constraints.maxWidth,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: colors.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: colors.borderColor),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 14, color: colors.iconColor),
              const SizedBox(width: 6),
              if (text.isNotEmpty)
                Flexible(
                  child: Text(
                    text,
                    style: TextStyle(
                      color: colors.textColor,
                      fontSize: fontSize,
                      fontWeight: fontBold ? FontWeight.bold : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                    softWrap: false,
                    maxLines: 1,
                  ),
                ),
            ],
          ),
        );

        if (onTap != null) {
          return GestureDetector(
            onTap: onTap,
            child: chip,
          );
        }

        return chip;
      }
    );
  }

  _ChipColors _getColors(ChipColorScheme scheme) {
    switch (scheme) {
      case ChipColorScheme.green:
        return _ChipColors(
          backgroundColor: const Color(0xFFE8F5E9), // Custom light green
          borderColor: const Color(0xFFA5D6A7), // Custom medium green
          iconColor: const Color(0xFF2E7D32), // Custom deep green
          textColor: const Color(0xFF1B5E20), // Custom darker green
        );
      case ChipColorScheme.blue:
        return _ChipColors(
          backgroundColor: const Color(0xFFE8F0FE), // Custom light blue
          borderColor: const Color(0xFFADC6FF), // Custom medium blue
          iconColor: const Color(0xFF3F51B5), // Custom deep blue
          textColor: const Color(0xFF303F9F), // Custom darker blue
        );
      case ChipColorScheme.teal:
        return _ChipColors(
          backgroundColor: const Color(0xFFE0F7FA), // Custom light cyan
          borderColor: const Color(0xFF80DEEA), // Custom medium cyan
          iconColor: const Color(0xFF00838F), // Custom deep cyan
          textColor: const Color(0xFF006064), // Custom darker cyan
        );
      case ChipColorScheme.purple:
        return _ChipColors(
          backgroundColor: const Color(0xFFF3E5F5), // Custom light purple
          borderColor: const Color(0xFFCE93D8), // Custom medium purple
          iconColor: const Color(0xFF7B1FA2), // Custom deep purple
          textColor: const Color(0xFF6A1B9A), // Custom darker purple
        );
      case ChipColorScheme.indigo:
        return _ChipColors(
          backgroundColor: const Color(0xFFE3F2FD), // Custom light sky blue
          borderColor: const Color(0xFF90CAF9), // Custom medium sky blue
          iconColor: const Color(0xFF1565C0), // Custom deep sky blue
          textColor: const Color(0xFF0D47A1), // Custom darker sky blue
        );
      case ChipColorScheme.red:
        return _ChipColors(
          backgroundColor: const Color(0xFFFFEBEE), // Custom light red
          borderColor: const Color(0xFFFFCDD2), // Custom medium red
          iconColor: const Color(0xFFD32F2F), // Custom deep red
          textColor: const Color(0xFFB71C1C), // Custom darker red
        );
      case ChipColorScheme.custom:
        // This will be handled by custom colors passed to the widget
        return _ChipColors(
          backgroundColor: Colors.grey.shade50,
          borderColor: Colors.grey.shade200,
          iconColor: Colors.grey.shade700,
          textColor: Colors.grey.shade700,
        );
    }
  }
}

class _ChipColors {
  final Color backgroundColor;
  final Color borderColor;
  final Color iconColor;
  final Color textColor;

  _ChipColors({
    required this.backgroundColor,
    required this.borderColor,
    required this.iconColor,
    required this.textColor,
  });
}

/// A specialized version of StyledInfoChip for status indicators
class StatusChip extends StatelessWidget {
  final String status;
  final bool isPaid;
  final bool isPartiallyPaid;
  final double fontSize;

  const StatusChip({
    super.key,
    required this.status,
    this.isPaid = false,
    this.isPartiallyPaid = false,
    this.fontSize = 12,
  });

  @override
  Widget build(BuildContext context) {
    late IconData icon;
    late ChipColorScheme colorScheme;
    Color? customTextColor;

    if (isPaid) {
      icon = Icons.check_circle;
      colorScheme = ChipColorScheme.green;
      customTextColor =
          Colors.white; // Use white text for PAID status on green background
    } else if (isPartiallyPaid) {
      icon = Icons.pending;
      colorScheme = ChipColorScheme.blue;
    } else {
      icon = Icons.unpublished;
      colorScheme = ChipColorScheme.red;
    }

    return StyledInfoChip(
      icon: icon,
      text: status,
      colorScheme: colorScheme,
      fontSize: fontSize,
      fontBold: true,
      customTextColor: customTextColor,
    );
  }
}

/// A specialized StyledInfoChip with properties optimized for showing currency amounts
class AmountChip extends StatelessWidget {
  final double amount;
  final bool showLabel;
  final Color? customColor;
  final double? fontSize;
  final double? discountAmount;

  const AmountChip({
    super.key,
    required this.amount,
    this.discountAmount,
    this.showLabel = true,
    this.customColor,
    this.fontSize = 12,
  });

  @override
  Widget build(BuildContext context) {
    final String amountText = showLabel
        ? 'Amount: ${CurrencyService.formatCurrency(amount)}'
        : CurrencyService.formatCurrency(amount);

    // Set up colors based on whether a custom color is provided
    final ChipColorScheme colorScheme =
        customColor != null ? ChipColorScheme.custom : ChipColorScheme.indigo;

    // If there's a discount, show it in a more compact layout to prevent overflow
    if (discountAmount != null && discountAmount! > 0) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          StyledInfoChip(
            icon: Icons.payments,
            text: amountText,
            colorScheme: colorScheme,
            customIconColor: customColor,
            customTextColor: customColor,
            customBackgroundColor: customColor?.withValues(alpha: 0.1),
            customBorderColor: customColor?.withValues(alpha: 0.3),
            fontSize: fontSize ?? 12,
          ),
          const SizedBox(height: 4),
          StyledInfoChip(
            icon: Icons.money_off,
            text: '-${CurrencyService.formatCurrency(discountAmount!)}',
            colorScheme: ChipColorScheme.blue,
            fontSize: fontSize ?? 12,
          ),
        ],
      );
    }

    // Simple amount display
    return StyledInfoChip(
      icon: Icons.payments,
      text: amountText,
      colorScheme: colorScheme,
      customIconColor: customColor,
      customTextColor: customColor,
      customBackgroundColor: customColor?.withValues(alpha: 0.1),
      customBorderColor: customColor?.withValues(alpha: 0.3),
      fontSize: fontSize ?? 12,
    );
  }
}

/// A specialized StyledInfoChip for displaying time durations
class DurationChip extends StatelessWidget {
  final int hours;
  final int minutes;
  final bool showLabel;
  final Color? customColor;

  const DurationChip({
    super.key,
    required this.hours,
    required this.minutes,
    this.showLabel = true,
    this.customColor,
  });

  @override
  Widget build(BuildContext context) {
    final color = customColor ?? Colors.teal.shade700;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.timer,
            color: color,
            size: 14,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              showLabel
                ? "Duration: ${hours}hr ${minutes}min"
                : "${hours}hr ${minutes}min",
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
