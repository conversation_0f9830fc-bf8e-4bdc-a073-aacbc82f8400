import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/backup_service.dart';
import '../services/permission_service.dart';
import '../services/file_service.dart';
import '../widgets/permission_error_dialog.dart';

/// Dialog for backup confirmation
class BackupDialog extends StatefulWidget {
  final BackupService _backupService;

  const BackupDialog({
    super.key,
    required BackupService backupService,
  }) : _backupService = backupService;

  @override
  State<BackupDialog> createState() => _BackupDialogState();
}

class _BackupDialogState extends State<BackupDialog> {
  bool _isBackingUp = false;

  Future<void> _performBackup() async {
    setState(() {
      _isBackingUp = true;
    });

    try {
      // Check permission status first
      final permissionStatus = await PermissionService.checkStoragePermissionStatus();

      if (!mounted) return;

      // If permission is not granted, show our custom dialog
      if (!permissionStatus) {
        Navigator.pop(context); // Close the confirmation dialog

        // Show our custom permission dialog
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required to save backups to your device.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Try backup again after permission is granted
              showDialog(
                context: context,
                builder: (context) => BackupDialog(backupService: widget._backupService),
              );
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );
        return;
      }

      // Close the confirmation dialog
      if (mounted) {
        Navigator.pop(context);
      }

      // Perform the backup
      final result = await widget._backupService.backupToLocalDevice(context);

      if (!mounted) return;

      if (result['success']) {
        final snackBar = SnackBar(
          content: Text('Backup saved to: ${result['path']}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'VIEW',
            textColor: Colors.white,
            onPressed: () async {
              // Always try to open the file location (directory) instead of the file itself
              final filePath = result['path'] as String;
              final file = File(filePath);
              final directoryPath = file.parent.path;

              // Try to open the directory location
              final success = await FileService.openFileLocation(directoryPath);

              if (!success && mounted) {
                // If opening the directory fails, show a more helpful message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Could not open backup location. Your backup is saved at: $directoryPath'),
                    backgroundColor: Colors.orange,
                    duration: const Duration(seconds: 8),
                  ),
                );
              }
            },
          ),
        );
        ScaffoldMessenger.of(context).showSnackBar(snackBar);
      } else {
        // Check if the error is related to permissions
        if (result['error'].toString().contains('permission')) {
          // Show our custom permission dialog for permission-related errors
          await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => PermissionErrorDialog(
              title: 'Storage Permission Required',
              message: 'Storage permission is required to save backups to your device.',
              permissionType: 'storage',
              onPermissionGranted: () {
                // Try backup again after permission is granted
                showDialog(
                  context: context,
                  builder: (context) => BackupDialog(backupService: widget._backupService),
                );
              },
              onCancel: () {
                // Do nothing, dialog will be dismissed
              },
            ),
          );
        } else {
          // For other errors, show a snackbar
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Backup failed: ${result['error'] ?? 'Please check storage permissions and try again.'}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isBackingUp = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Backup Confirmation'),
      content: const Text('Do you want to create a backup of your data?'),
      actions: [
        TextButton(
          onPressed: _isBackingUp ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isBackingUp ? null : _performBackup,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          child: _isBackingUp
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : const Text('Backup'),
        ),
      ],
    );
  }
}

/// Dialog for configuring scheduled backups
class ScheduledBackupDialog extends StatefulWidget {
  final BackupService _backupService;

  const ScheduledBackupDialog({
    super.key,
    required BackupService backupService,
  }) : _backupService = backupService;

  @override
  State<ScheduledBackupDialog> createState() => _ScheduledBackupDialogState();
}

class _ScheduledBackupDialogState extends State<ScheduledBackupDialog> {
  late bool _isEnabled;
  late int _intervalHours;
  bool _isLoading = false;
  bool _hasStoragePermission = false;

  @override
  void initState() {
    super.initState();
    final settings = widget._backupService.getScheduledBackupSettings();
    _isEnabled = settings['enabled'] as bool;
    _intervalHours = settings['interval'] as int;

    // Check if storage permission is already granted
    _checkStoragePermission();
  }

  Future<void> _checkStoragePermission() async {
    final status = await Permission.storage.status;
    if (mounted) {
      setState(() {
        _hasStoragePermission = status.isGranted;
      });
    }
  }

  Future<void> _requestStoragePermission() async {
    if (!mounted) return;

    // Show our custom permission dialog
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PermissionErrorDialog(
        title: 'Storage Permission Required',
        message: 'Storage permission is required for scheduled backups to work properly.',
        permissionType: 'storage',
        onPermissionGranted: () {
          // Update state after permission is granted
          if (mounted) {
            setState(() {
              _hasStoragePermission = true;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Storage permission granted'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        onCancel: () {
          // Do nothing, dialog will be dismissed
        },
      ),
    );

    // Check permission status after dialog is closed
    if (mounted) {
      final status = await PermissionService.checkStoragePermissionStatus();
      setState(() {
        _hasStoragePermission = status;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // If enabling scheduled backups, check for storage permission
      if (_isEnabled && !_hasStoragePermission) {
        // Show our custom permission dialog
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required for scheduled backups to work properly.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Update state after permission is granted
              if (mounted) {
                setState(() {
                  _hasStoragePermission = true;
                });
              }
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );

        // Check permission status after dialog is closed
        if (mounted) {
          final status = await PermissionService.checkStoragePermissionStatus();
          setState(() {
            _hasStoragePermission = status;
          });

          // If still not granted, show warning but allow saving settings anyway
          if (!_hasStoragePermission) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Warning: Storage permission not granted. Scheduled backups may not work properly.',
                  ),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 5),
                ),
              );
            }
          }
        }
      }

      await widget._backupService.setScheduledBackup(
        enabled: _isEnabled,
        intervalHours: _intervalHours,
      );

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEnabled
                  ? 'Scheduled backup enabled'
                  : 'Scheduled backup disabled',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Scheduled Backup Settings'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SwitchListTile(
              title: const Text('Enable Scheduled Backup'),
              value: _isEnabled,
              activeColor: Colors.blue,
              activeTrackColor: Colors.blue
                  .withValues(red: 0, green: 0, blue: 255, alpha: 0.5),
              inactiveThumbColor: Colors.grey[300],
              inactiveTrackColor: Colors.grey[400],
              onChanged: (value) {
                setState(() {
                  _isEnabled = value;
                });
              },
            ),
            if (!_hasStoragePermission) ...[
              _buildPermissionWarning(),
            ] else ...[
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  'Storage permission granted ✓',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
            if (_isEnabled) ...[
              _buildIntervalSettings(),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveSettings,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.cyan,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildPermissionWarning() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: Colors.red.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Storage permission not granted',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Scheduled backups require storage permission to work properly.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _requestStoragePermission,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.cyan,
                foregroundColor: Colors.white,
                minimumSize: const Size.fromHeight(36),
              ),
              child: const Text('Grant Permission'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIntervalSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        const Text('Backup Interval'),
        Slider(
          value: _intervalHours.toDouble(),
          min: 1,
          max: 168, // 1 week
          divisions: 167,
          label: '$_intervalHours hours',
          activeColor: Colors.cyan,
          thumbColor: Colors.cyan,
          inactiveColor:
              Colors.cyan.withValues(red: 0, green: 255, blue: 255, alpha: 0.2),
          onChanged: (value) {
            setState(() {
              _intervalHours = value.round();
            });
          },
        ),
        Text(
          'Backup every $_intervalHours hours',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }


}
