import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/utils/navigation_helper.dart';

class CustomerFormScreen extends StatefulWidget {
  final Customer? customer;
  final bool isNewCustomer;

  const CustomerFormScreen({
    super.key,
    this.customer,
    this.isNewCustomer = false,
  });

  @override
  State<CustomerFormScreen> createState() => _CustomerFormScreenState();
}

class _CustomerFormScreenState extends State<CustomerFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _contactNumberController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.customer != null) {
      _nameController.text = widget.customer!.name;
      _contactNumberController.text = widget.customer!.contactNumber ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _contactNumberController.dispose();
    super.dispose();
  }

  // Helper method to paste text from clipboard to a text controller
  Future<void> _pasteFromClipboard(TextEditingController controller) async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null && mounted) {
        controller.text = clipboardData!.text!;
        // Show feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Text pasted from clipboard'),
              duration: Duration(seconds: 1),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error pasting from clipboard: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not paste: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.customer != null && !widget.isNewCustomer;
    final isImportingContact = widget.isNewCustomer && widget.customer != null;
    final appBarTitle = isEditing
        ? 'Edit Customer'
        : (isImportingContact ? 'Import Contact' : 'Add Customer');

    return Scaffold(
      appBar: AppBar(
        title: Text(appBarTitle),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => NavigationHelper.goBack(context, false),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Customer Information Card
                    Card(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                    widget.isNewCustomer
                                        ? Icons.contact_phone
                                        : Icons.person,
                                    color: Colors.blue.shade700),
                                const SizedBox(width: 8),
                                Text(
                                  widget.isNewCustomer
                                      ? 'Contact Information'
                                      : 'Customer Information',
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Divider(height: 1, thickness: 1),
                            const SizedBox(height: 16),

                            // Name Field
                            TextFormField(
                              controller: _nameController,
                              decoration: InputDecoration(
                                labelText: 'Name',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(Icons.person_outline,
                                    color: Colors.blue.shade700),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                // Add suffix icon for paste functionality
                                suffixIcon: IconButton(
                                  icon: const Icon(Icons.paste),
                                  tooltip: 'Paste',
                                  onPressed: () => _pasteFromClipboard(_nameController),
                                ),
                              ),
                              enableInteractiveSelection: true,
                              contextMenuBuilder: (context, editableTextState) {
                                return AdaptiveTextSelectionToolbar.editableText(
                                  editableTextState: editableTextState,
                                );
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a name';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Contact Number Field
                            TextFormField(
                              controller: _contactNumberController,
                              decoration: InputDecoration(
                                labelText: 'Contact Number (Optional)',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(Icons.phone,
                                    color: Colors.green.shade700),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                // Add suffix icon for paste functionality
                                suffixIcon: IconButton(
                                  icon: const Icon(Icons.paste),
                                  tooltip: 'Paste',
                                  onPressed: () => _pasteFromClipboard(_contactNumberController),
                                ),
                              ),
                              enableInteractiveSelection: true,
                              contextMenuBuilder: (context, editableTextState) {
                                return AdaptiveTextSelectionToolbar.editableText(
                                  editableTextState: editableTextState,
                                );
                              },
                              keyboardType: TextInputType.phone,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Save Button
                    ElevatedButton(
                      onPressed: _saveCustomer,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7D32),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          isEditing ? 'Update Customer' : 'Add Customer',
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Future<void> _saveCustomer() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Clean up phone number if provided
        final String cleanedPhoneNumber = _contactNumberController.text.trim();

        // Create or update customer object
        Customer customer;

        if (widget.customer == null) {
          // Create a new customer
          customer = Customer(
            name: _nameController.text.trim(),
            contactNumber: cleanedPhoneNumber.isEmpty ? null : cleanedPhoneNumber,
            balance: 0.0, // Explicitly set the balance for new customers
            createdAt: DateTime.now(), // Set creation time for new customers
          );

          // Try to save the customer with retries
          await _saveCustomerWithRetry(customer);

          debugPrint('New customer created with ID: ${customer.id}');
        } else {
          // Update existing customer
          customer = widget.customer!;
          customer.name = _nameController.text.trim();
          customer.contactNumber = cleanedPhoneNumber.isEmpty ? null : cleanedPhoneNumber;

          // For customers from contacts, we need to save them as new if they don't have an ID
          if (widget.isNewCustomer && customer.id == 0) {
            // This is a new customer from contacts
            debugPrint('Saving new customer from contacts');
            await _saveCustomerWithRetry(customer);
          } else {
            // This is an existing customer being updated
            await _updateCustomerWithRetry(customer);
          }
        }

        // Return success result and show feedback
        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.isNewCustomer
                    ? 'Customer imported successfully'
                    : (widget.customer != null ? 'Customer updated successfully' : 'Customer added successfully'),
              ),
              backgroundColor: const Color(0xFF2E7D32),
              duration: const Duration(seconds: 2),
            ),
          );

          // Return true to indicate success with smooth transition
          NavigationHelper.goBack(context, true);
        }
      } catch (e) {
        debugPrint('Error in _saveCustomer: $e');

        if (mounted) {
          // Show detailed error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: 'RETRY',
                textColor: Colors.white,
                onPressed: _saveCustomer,
              ),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // Helper method to save customer with retry mechanism
  Future<void> _saveCustomerWithRetry(Customer customer, {int retryCount = 0}) async {
    try {
      // Try to save the customer
      final int newId = await DatabaseService.saveCustomer(customer);

      // Update the customer ID with the new ID from the database
      if (newId > 0) {
        customer.id = newId;
        debugPrint('Customer saved with ID: $newId');
      } else {
        debugPrint('Warning: Customer saved but returned ID is $newId');
      }
    } catch (e) {
      // Log the error
      debugPrint('Error saving customer (attempt ${retryCount + 1}): $e');

      // Retry up to 2 times with a short delay
      if (retryCount < 2) {
        await Future.delayed(Duration(milliseconds: 500 * (retryCount + 1)));
        return _saveCustomerWithRetry(customer, retryCount: retryCount + 1);
      }

      // If all retries fail, rethrow the error
      rethrow;
    }
  }

  // Helper method to update customer with retry mechanism
  Future<void> _updateCustomerWithRetry(Customer customer, {int retryCount = 0}) async {
    try {
      // Try to update the customer
      await DatabaseService.updateCustomer(customer);
    } catch (e) {
      // Log the error
      debugPrint('Error updating customer (attempt ${retryCount + 1}): $e');

      // Retry up to 2 times with a short delay
      if (retryCount < 2) {
        await Future.delayed(Duration(milliseconds: 500 * (retryCount + 1)));
        return _updateCustomerWithRetry(customer, retryCount: retryCount + 1);
      }

      // If all retries fail, rethrow the error
      rethrow;
    }
  }
}
