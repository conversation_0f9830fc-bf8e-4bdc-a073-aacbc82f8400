import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:tubewell_water_billing_app/models/bill.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/models/expense.dart';
import 'package:tubewell_water_billing_app/models/payment.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/services/expense_service.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';
import 'package:tubewell_water_billing_app/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing_app/widgets/app_drawer.dart';
import 'package:tubewell_water_billing_app/widgets/date_filter_dropdown.dart';

/// A screen that displays comprehensive summary statistics for the entire app
class SummaryScreen extends StatefulWidget {
  const SummaryScreen({super.key});

  @override
  State<SummaryScreen> createState() => _SummaryScreenState();
}

class _SummaryScreenState extends State<SummaryScreen>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  // Tab controller
  TabController? _tabController;
  static const int _tabCount = 4; // Matches the number of tabs: Overall, Bills, Payments, Expenses
  // State variables
  bool _isLoading = true;
  bool _isRefreshing = false;

  // Date range for filtering
  DateTime? _startDate;
  DateTime? _endDate;
  DateFilterOption _selectedDateFilter = DateFilterOption.thisMonth;

  // Summary data
  Map<String, double> _accountSummary = {
    'income': 0.0,
    'expenses': 0.0,
    'netBalance': 0.0,
  };

  Map<String, num> _billsSummary = {
    'totalCount': 0,
    'totalAmount': 0.0,
    'paidAmount': 0.0,
    'unpaidAmount': 0.0,
    'paidCount': 0,
    'unpaidCount': 0,
  };

  Map<String, double> _expenseSummary = {
    'totalAmount': 0.0,
    'count': 0.0,
    'averageAmount': 0.0,
    'maxAmount': 0.0,
    'minAmount': 0.0,
  };

  // Category breakdowns
  Map<String, double> _expenseCategories = {};
  Map<String, double> _paymentMethods = {};

  // Recent data for detailed view
  List<Bill> _recentBills = [];
  List<Payment> _recentPayments = [];
  List<Expense> _recentExpenses = [];
  Map<int, Customer> _customersMap = {};

  // Subscription for data changes
  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _initTabController();

    // Initialize date range based on selected filter
    _initializeDateRange();

    // Load all data
    _loadAllData();

    // Subscribe to data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      // Refresh data when relevant changes occur
      if (changeType == DataChangeType.bill ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.expense ||
          changeType == DataChangeType.customer ||
          changeType == DataChangeType.all) {
        _refreshData();
      }
    });
  }

  void _initializeDateRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (_selectedDateFilter) {
      case DateFilterOption.today:
        _startDate = today;
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last7Days:
        _startDate = today.subtract(const Duration(days: 6));
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.thisMonth:
        _startDate = DateTime(today.year, today.month, 1);
        // Last day of current month
        final nextMonth = today.month < 12
            ? DateTime(today.year, today.month + 1, 1)
            : DateTime(today.year + 1, 1, 1);
        _endDate = nextMonth.subtract(const Duration(days: 1));
        _endDate = DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
        break;
      case DateFilterOption.thisYear:
        _startDate = DateTime(today.year, 1, 1);
        _endDate = DateTime(today.year, 12, 31, 23, 59, 59);
        break;
      case DateFilterOption.custom:
        // Default to last 30 days if custom is selected initially
        _startDate = today.subtract(const Duration(days: 30));
        _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
    }
  }

  void _initTabController() {
    // Dispose existing controller if any
    _tabController?.dispose();

    // Create new controller with correct length
    _tabController = TabController(
      length: _tabCount,
      vsync: this,
      initialIndex: 0, // Explicitly set initial index
    );

    // Add listener to update UI when tab changes
    _tabController!.addListener(() {
      if (_tabController!.indexIsChanging) {
        setState(() {
          // This will trigger a rebuild to update tab colors
        });
      }
    });
  }

  @override
  void didUpdateWidget(SummaryScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Ensure tab controller is recreated if needed
    if (_tabController == null || _tabController!.length != _tabCount) {
      _initTabController();
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _dataChangeSubscription.cancel();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadAllData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Load all data in parallel for better performance
      await Future.wait([
        _loadAccountSummary(),
        _loadBillsSummary(),
        _loadExpenseSummary(),
        _loadRecentData(),
      ]);
    } catch (e) {
      // Handle errors
      debugPrint('Error loading summary data: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      await _loadAllData();
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  Future<void> _loadAccountSummary() async {
    final summary = await DatabaseService.getAccountSummary(
      startDate: _startDate,
      endDate: _endDate,
    );

    if (mounted) {
      setState(() {
        _accountSummary = summary;
      });
    }
  }

  Future<void> _loadBillsSummary() async {
    final summary = await DatabaseService.getBillsSummaryEfficient(
      startDate: _startDate,
      endDate: _endDate,
    );

    if (mounted) {
      setState(() {
        _billsSummary = summary;
      });
    }
  }

  Future<void> _loadExpenseSummary() async {
    final summary = await ExpenseService.getExpenseSummary(
      startDate: _startDate,
      endDate: _endDate,
    );

    // Load expense categories breakdown
    final expenses = await DatabaseService.getAllExpenses(
      startDate: _startDate,
      endDate: _endDate,
      limit: 1000, // Get a large number to calculate accurate statistics
    );

    // Calculate category totals
    final Map<String, double> categoryTotals = {};
    for (final expense in expenses) {
      final category = expense.category;
      if (category.isNotEmpty) {
        categoryTotals[category] =
            (categoryTotals[category] ?? 0) + expense.amount;
      }
    }

    // Calculate payment method totals
    final payments = await DatabaseService.getAllPayments(
      startDate: _startDate,
      endDate: _endDate,
      limit: 1000, // Get a large number to calculate accurate statistics
    );

    final Map<String, double> methodTotals = {};
    for (final payment in payments) {
      final method = payment.paymentMethod ?? 'Unknown';
      methodTotals[method] = (methodTotals[method] ?? 0) + payment.amount;
    }

    if (mounted) {
      setState(() {
        _expenseSummary = summary;
        _expenseCategories = categoryTotals;
        _paymentMethods = methodTotals;
      });
    }
  }

  Future<void> _loadRecentData() async {
    // Load recent bills (last 5)
    final bills = await DatabaseService.getRecentBills(limit: 5);

    // Load recent payments (last 5)
    final payments = await DatabaseService.getRecentPayments(limit: 5);

    // Load recent expenses (last 5)
    final expenses = await DatabaseService.getRecentExpenses(limit: 5);

    // Load all customers for reference
    final customers = await DatabaseService.getAllCustomers();
    final Map<int, Customer> customersMap = {};
    for (final customer in customers) {
      customersMap[customer.id] = customer;
    }

    if (mounted) {
      setState(() {
        _recentBills = bills;
        _recentPayments = payments;
        _recentExpenses = expenses;
        _customersMap = customersMap;
      });
    }
  }

  Future<void> _generateSummaryPdf() async {
    if (!mounted) return;

    try {
      // Create PDF data
      final Map<String, dynamic> pdfData = {
        'summary': true, // Flag to indicate this is a summary report
        'accountSummary': _accountSummary,
        'billSummary': _billsSummary,
        'dateRange': {
          'startDate': _startDate,
          'endDate': _endDate,
        },
        'recentBills': _recentBills,
        'recentPayments': _recentPayments,
        'recentExpenses': _recentExpenses,
        'expenseCategories': _expenseCategories,
        'paymentMethods': _paymentMethods,
        'customersMap': _customersMap,
      };

      // Use the universal PDF service to generate the PDF
      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': 'Summary Report',
          },
        ),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Scaffold(
      appBar: TubewellAppBar(
        title: 'Dashboard',
        currentScreen: 'summary',
        showBackButton: false,
        onPdfPressed: _generateSummaryPdf,
      ),
      drawer: const AppDrawer(currentScreen: 'summary'),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // Tab bar - directly below AppBar
        Container(
          color: Colors.white,
          child: _tabController == null
              ? const SizedBox
                  .shrink() // Placeholder while controller is initializing
              : TabBar(
                  controller: _tabController,
                  isScrollable:
                      false, // Set to false to fit all tabs in one row
                  labelPadding: const EdgeInsets.symmetric(
                      horizontal: 6.0), // Reduce padding to fit all tabs
                  labelStyle: const TextStyle(
                      fontSize: 13), // Reduced text size by 1 point
                  indicator:
                      const BoxDecoration(), // Remove underline indicator
                  onTap: (index) {
                    setState(() {
                      // Update tab controller to reflect the selected tab
                      _tabController?.animateTo(index);
                    });
                  },
                  tabs: [
                    Tab(
                      icon: Icon(
                        Icons.dashboard,
                        size: 20,
                        color: _tabController?.index == 0
                            ? Colors.green.shade600
                            : Colors.cyan.shade600,
                      ),
                      child: Text(
                        'Overall',
                        style: TextStyle(
                          color: _tabController?.index == 0
                              ? Colors.green.shade600
                              : Colors.cyan.shade600,
                        ),
                      ),
                    ),
                    Tab(
                      icon: Icon(
                        Icons.receipt_long,
                        size: 20,
                        color: _tabController?.index == 1
                            ? Colors.green.shade600
                            : Colors.blue.shade600,
                      ),
                      child: Text(
                        'Bills',
                        style: TextStyle(
                          color: _tabController?.index == 1
                              ? Colors.green.shade600
                              : Colors.blue.shade600,
                        ),
                      ),
                    ),
                    Tab(
                      icon: Icon(
                        Icons.payment,
                        size: 20,
                        color: _tabController?.index == 2
                            ? Colors.green.shade600
                            : Colors.purple.shade600,
                      ),
                      child: Text(
                        'Payments',
                        style: TextStyle(
                          color: _tabController?.index == 2
                              ? Colors.green.shade600
                              : Colors.purple.shade600,
                        ),
                      ),
                    ),
                    Tab(
                      icon: Icon(
                        Icons.account_balance_wallet,
                        size: 20,
                        color: _tabController?.index == 3
                            ? Colors.green.shade600
                            : Colors.indigo.shade600,
                      ),
                      child: Text(
                        'Expenses',
                        style: TextStyle(
                          color: _tabController?.index == 3
                              ? Colors.green.shade600
                              : Colors.indigo.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
        ),

        // Tab content
        Expanded(
          child: _tabController == null
              ? const Center(
                  child: CircularProgressIndicator()) // Loading indicator
              : TabBarView(
                  controller: _tabController,
                  children: [
                    // Overall tab
                    RefreshIndicator(
                      onRefresh: _refreshData,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            _buildOverallSummaryCard(),
                            const SizedBox(height: 16),
                            _buildBillsBarChart(),
                            // Recent activity removed from overall tab as requested
                          ],
                        ),
                      ),
                    ),

                    // Bills tab
                    RefreshIndicator(
                      onRefresh: _refreshData,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            _buildBillsSummaryCard(),
                            const SizedBox(height: 16),
                            _buildBillsPieChart(),
                            const SizedBox(height: 16),
                            _buildRecentBillsCard(),
                          ],
                        ),
                      ),
                    ),

                    // Payments tab
                    RefreshIndicator(
                      onRefresh: _refreshData,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            _buildPaymentsSummaryCard(),
                            const SizedBox(height: 16),
                            _buildPaymentsPieChart(),
                            const SizedBox(height: 16),
                            _buildRecentPaymentsCard(),
                          ],
                        ),
                      ),
                    ),

                    // Expenses tab
                    RefreshIndicator(
                      onRefresh: _refreshData,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            _buildExpensesSummaryCard(),
                            const SizedBox(height: 16),
                            _buildExpensesPieChart(),
                            const SizedBox(height: 16),
                            _buildRecentExpensesCard(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
        ),
      ],
    );
  }

  Widget _buildOverallSummaryCard() {
    // Ensure we have valid summary values
    final double totalIncome = _accountSummary['income'] ?? 0.0;
    final double totalExpenses = _accountSummary['expenses'] ?? 0.0;
    final double netBalance = _accountSummary['netBalance'] ?? 0.0;

    // Calculate additional metrics
    final double totalBillAmount =
        _billsSummary['totalAmount']?.toDouble() ?? 0.0;
    final double paidAmount = _billsSummary['paidAmount']?.toDouble() ?? 0.0;
    final double unpaidAmount =
        _billsSummary['unpaidAmount']?.toDouble() ?? 0.0;
    // Payment rate calculation
    final double paymentRate =
        totalBillAmount > 0 ? (paidAmount / totalBillAmount) * 100 : 0.0;

    // Customer data

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          // Header
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Financial Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade900,
                  fontSize: 18,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Overall Statistics',
                    style: TextStyle(
                      color: Colors.blue.shade800,
                      fontSize: 14,
                    ),
                  ),
                  DateFilterDropdown(
                    selectedOption: _selectedDateFilter,
                    startDate: _startDate,
                    endDate: _endDate,
                    onDateRangeChanged: (startDate, endDate, selectedOption) {
                      if (selectedOption == DateFilterOption.custom && startDate != null && endDate != null) {
                        setState(() {
                          _selectedDateFilter = DateFilterOption.custom;
                        });
                        _onDateRangeChanged(startDate, endDate);
                      } else {
                        _onDateFilterChanged(selectedOption, 0);
                      }
                    },
                    backgroundColor: Colors.blue.shade50,
                    textColor: Colors.blue.shade800,
                    iconColor: Colors.blue.shade700,
                    borderColor: Colors.blue.shade200,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.purple.shade200),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance,
                      color: Colors.purple.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Net Balance',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.purple.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    CurrencyService.formatCurrency(netBalance),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: netBalance >= 0
                          ? Colors.green.shade700
                          : Colors.red.shade700,
                      fontSize: 22,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Income and Expenses
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.teal.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.teal.shade200),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.arrow_upward,
                            color: Colors.teal.shade700,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Total Income',
                            style: TextStyle(
                              color: Colors.teal.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        CurrencyService.formatCurrency(totalIncome),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.teal.shade800,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.indigo.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.indigo.shade200),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.arrow_downward,
                            color: Colors.indigo.shade700,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Total Expenses',
                            style: TextStyle(
                              color: Colors.indigo.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        CurrencyService.formatCurrency(totalExpenses),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo.shade800,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Bill Metrics Row 1
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long,
                            color: Colors.blue.shade700,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Total Bills',
                            style: TextStyle(
                              color: Colors.blue.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        CurrencyService.formatCurrency(totalBillAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green.shade700,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Paid Bills',
                            style: TextStyle(
                              color: Colors.green.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        CurrencyService.formatCurrency(paidAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Bill Metrics Row 2
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.money_off,
                            color: Colors.red.shade700,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Unpaid Bills',
                            style: TextStyle(
                              color: Colors.red.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        CurrencyService.formatCurrency(unpaidAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade800,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.deepPurple.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.deepPurple.shade200),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.payment,
                            color: Colors.deepPurple.shade700,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Payment Rate',
                            style: TextStyle(
                              color: Colors.deepPurple.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${paymentRate.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.deepPurple.shade800,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBillsSummaryCard() {
    // Ensure we have valid summary values
    final int totalCount = _billsSummary['totalCount']?.toInt() ?? 0;
    final double totalAmount = _billsSummary['totalAmount']?.toDouble() ?? 0.0;
    final double paidAmount = _billsSummary['paidAmount']?.toDouble() ?? 0.0;
    final double unpaidAmount =
        _billsSummary['unpaidAmount']?.toDouble() ?? 0.0;
    final int paidCount = _billsSummary['paidCount']?.toInt() ?? 0;
    final int unpaidCount = _billsSummary['unpaidCount']?.toInt() ?? 0;

    // Calculate payment percentage
    final double paymentPercentage =
        totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Header
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bills Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade900,
                  fontSize: 18,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$totalCount bills',
                    style: TextStyle(
                      color: Colors.blue.shade800,
                      fontSize: 14,
                    ),
                  ),
                  DateFilterDropdown(
                    selectedOption: _selectedDateFilter,
                    startDate: _startDate,
                    endDate: _endDate,
                    onDateRangeChanged: (startDate, endDate, selectedOption) {
                      if (selectedOption == DateFilterOption.custom && startDate != null && endDate != null) {
                        setState(() {
                          _selectedDateFilter = DateFilterOption.custom;
                        });
                        _onDateRangeChanged(startDate, endDate);
                      } else {
                        _onDateFilterChanged(selectedOption, 1);
                      }
                    },
                    backgroundColor: Colors.blue.shade50,
                    textColor: Colors.blue.shade800,
                    iconColor: Colors.blue.shade700,
                    borderColor: Colors.blue.shade200,
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.receipt_long,
                      color: Colors.blue.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Bill Amount',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    CurrencyService.formatCurrency(totalAmount),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade900,
                      fontSize: 22,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Payment progress indicator
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Payment Progress',
                    style: TextStyle(
                      color: Colors.blue.shade800,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    '${paymentPercentage.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: totalAmount > 0 ? paidAmount / totalAmount : 0,
                backgroundColor: Colors.red.shade100,
                valueColor:
                    AlwaysStoppedAnimation<Color>(Colors.green.shade500),
                minHeight: 10,
                borderRadius: BorderRadius.circular(5),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Detailed stats
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Paid',
                  value: CurrencyService.formatCurrency(paidAmount),
                  subtitle: '$paidCount bills',
                  icon: Icons.check_circle,
                  iconColor: Colors.green.shade700,
                  bgColor: Colors.green.shade50,
                  borderColor: Colors.green.shade200,
                  textColor: Colors.green.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Unpaid',
                  value: CurrencyService.formatCurrency(unpaidAmount),
                  subtitle: '$unpaidCount bills',
                  icon: Icons.money_off,
                  iconColor: Colors.red.shade700,
                  bgColor: Colors.red.shade50,
                  borderColor: Colors.red.shade200,
                  textColor: Colors.red.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExpensesSummaryCard() {
    // Ensure we have valid summary values
    final double totalAmount = _expenseSummary['totalAmount'] ?? 0.0;
    final double count = _expenseSummary['count'] ?? 0.0;
    final double averageAmount = _expenseSummary['averageAmount'] ?? 0.0;
    final double maxAmount = _expenseSummary['maxAmount'] ?? 0.0;
    final double minAmount = _expenseSummary['minAmount'] ?? 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Header
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Expenses Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo.shade900,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${count.toInt()} expenses',
                    style: TextStyle(
                      color: Colors.indigo.shade800,
                      fontSize: 14,
                    ),
                  ),
                  DateFilterDropdown(
                    selectedOption: _selectedDateFilter,
                    startDate: _startDate,
                    endDate: _endDate,
                    onDateRangeChanged: (startDate, endDate, selectedOption) {
                      if (selectedOption == DateFilterOption.custom && startDate != null && endDate != null) {
                        setState(() {
                          _selectedDateFilter = DateFilterOption.custom;
                        });
                        _onDateRangeChanged(startDate, endDate);
                      } else {
                        _onDateFilterChanged(selectedOption, 3);
                      }
                    },
                    backgroundColor: Colors.indigo.shade50,
                    textColor: Colors.indigo.shade800,
                    iconColor: Colors.indigo.shade700,
                    borderColor: Colors.indigo.shade200,
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.indigo.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.indigo.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Colors.indigo.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Expenses',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.indigo.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    CurrencyService.formatCurrency(totalAmount),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo.shade900,
                      fontSize: 22,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Detailed stats
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Average',
                  value: CurrencyService.formatCurrency(averageAmount),
                  subtitle: 'Per expense',
                  icon: Icons.calculate,
                  iconColor: Colors.indigo.shade700,
                  bgColor: Colors.indigo.shade50,
                  borderColor: Colors.indigo.shade200,
                  textColor: Colors.indigo.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Range',
                  value:
                      '${CurrencyService.formatCurrency(minAmount)} - ${CurrencyService.formatCurrency(maxAmount)}',
                  subtitle: 'Min - Max',
                  icon: Icons.trending_up,
                  iconColor: Colors.teal.shade700,
                  bgColor: Colors.teal.shade50,
                  borderColor: Colors.teal.shade200,
                  textColor: Colors.teal.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    String? subtitle,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color borderColor,
    required Color textColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: iconColor,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: textColor,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color:
                    textColor.withValues(alpha: 204), // 0.8 opacity (204/255)
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  // Helper method to build section headers if needed in the future
  // Currently unused but kept for potential future use
  /*
  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Row(
      children: [
        Icon(
          icon,
          color: color,
          size: 18,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
        ),
      ],
    );
  }
  */

  Widget _buildRecentItemTile({
    required String title,
    required String subtitle,
    required double amount,
    required IconData icon,
    required Color iconColor,
    required String status,
    bool isExpense = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 26), // 0.1 opacity
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                CurrencyService.formatCurrency(amount),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color:
                      isExpense ? Colors.red.shade700 : Colors.green.shade700,
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getStatusColor(status)
                      .withValues(alpha: 26), // 0.1 opacity
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  status,
                  style: TextStyle(
                    color: _getStatusColor(status),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Currently unused detail card for bills
  // Commented out to avoid linting warnings
  /*
  Widget _buildBillsDetailCard() {
    // Ensure we have valid summary values
    final int totalCount = _billsSummary['totalCount']?.toInt() ?? 0;
    final double totalAmount = _billsSummary['totalAmount']?.toDouble() ?? 0.0;
    final double paidAmount = _billsSummary['paidAmount']?.toDouble() ?? 0.0;
    final int paidCount = _billsSummary['paidCount']?.toInt() ?? 0;
    final int unpaidCount = _billsSummary['unpaidCount']?.toInt() ?? 0;

    // Calculate additional metrics
    final double avgBillAmount =
        totalCount > 0 ? totalAmount / totalCount : 0.0;
    final double paidPercentage =
        totalCount > 0 ? (paidCount / totalCount) * 100 : 0.0;
    final double unpaidPercentage =
        totalCount > 0 ? (unpaidCount / totalCount) * 100 : 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bills Details',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade900,
              fontSize: 18,
            ),
          ),

          const SizedBox(height: 16),

          // Monthly comparison (placeholder for now)
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Average Bill',
                  value: CurrencyService.formatCurrency(avgBillAmount),
                  icon: Icons.analytics,
                  iconColor: Colors.blue.shade700,
                  bgColor: Colors.blue.shade50,
                  borderColor: Colors.blue.shade200,
                  textColor: Colors.blue.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Paid Ratio',
                  value: '${paidPercentage.toStringAsFixed(1)}%',
                  subtitle: '$paidCount of $totalCount bills',
                  icon: Icons.pie_chart,
                  iconColor: Colors.green.shade700,
                  bgColor: Colors.green.shade50,
                  borderColor: Colors.green.shade200,
                  textColor: Colors.green.shade800,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Additional metrics
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Unpaid Ratio',
                  value: '${unpaidPercentage.toStringAsFixed(1)}%',
                  subtitle: '$unpaidCount of $totalCount bills',
                  icon: Icons.pie_chart,
                  iconColor: Colors.red.shade700,
                  bgColor: Colors.red.shade50,
                  borderColor: Colors.red.shade200,
                  textColor: Colors.red.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Collection Rate',
                  value: totalAmount > 0
                      ? '${((paidAmount / totalAmount) * 100).toStringAsFixed(1)}%'
                      : '0.0%',
                  icon: Icons.trending_up,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  */

  // Currently unused detail card for customers
  // Commented out to avoid linting warnings
  /*
  Widget _buildCustomersDetailCard() {
    // Ensure we have valid summary values
    final int totalCount = _customersSummary['totalCount']?.toInt() ?? 0;
    final int activeCount = _customersSummary['activeCount']?.toInt() ?? 0;
    final double totalCredit = _customersSummary['totalCredit']?.toDouble() ?? 0.0;
    final double totalDue = _customersSummary['totalDue']?.toDouble() ?? 0.0;

    // Calculate additional metrics
    final double avgCredit = activeCount > 0 ? totalCredit / activeCount : 0.0;
    final double avgDue = activeCount > 0 ? totalDue / activeCount : 0.0;
    final int inactiveCount = totalCount - activeCount;
    final double inactivePercentage =
        totalCount > 0 ? (inactiveCount / totalCount) * 100 : 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customers Details',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.purple.shade900,
              fontSize: 18,
            ),
          ),

          const SizedBox(height: 16),

          // Average metrics
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Avg Credit',
                  value: CurrencyService.formatCurrency(avgCredit),
                  subtitle: 'Per active customer',
                  icon: Icons.account_balance_wallet,
                  iconColor: Colors.green.shade700,
                  bgColor: Colors.green.shade50,
                  borderColor: Colors.green.shade200,
                  textColor: Colors.green.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Avg Due',
                  value: CurrencyService.formatCurrency(avgDue),
                  subtitle: 'Per active customer',
                  icon: Icons.money_off,
                  iconColor: Colors.red.shade700,
                  bgColor: Colors.red.shade50,
                  borderColor: Colors.red.shade200,
                  textColor: Colors.red.shade800,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Additional metrics
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Inactive',
                  value: inactiveCount.toInt().toString(),
                  subtitle:
                      '${inactivePercentage.toStringAsFixed(1)}% of total',
                  icon: Icons.person_off,
                  iconColor: Colors.grey.shade700,
                  bgColor: Colors.grey.shade50,
                  borderColor: Colors.grey.shade200,
                  textColor: Colors.grey.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Net Balance',
                  value: CurrencyService.formatCurrency(totalCredit - totalDue),
                  icon: Icons.account_balance,
                  iconColor: Colors.blue.shade700,
                  bgColor: Colors.blue.shade50,
                  borderColor: Colors.blue.shade200,
                  textColor: Colors.blue.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  */

  Widget _buildPaymentsSummaryCard() {
    // Calculate payment totals
    double totalAmount = 0.0;
    double billPayments = 0.0;
    double creditPayments = 0.0;
    int paymentCount = _recentPayments.length;

    // Only calculate totals if there are payments
    if (paymentCount > 0) {
      for (var payment in _recentPayments) {
        totalAmount += payment.amount;
        if (payment.billId > 0) {
          billPayments += payment.amount;
        } else {
          creditPayments += payment.amount;
        }
      }
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Header
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Payments Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple.shade900,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$paymentCount payments',
                    style: TextStyle(
                      color: Colors.purple.shade800,
                      fontSize: 14,
                    ),
                  ),
                  DateFilterDropdown(
                    selectedOption: _selectedDateFilter,
                    startDate: _startDate,
                    endDate: _endDate,
                    onDateRangeChanged: (startDate, endDate, selectedOption) {
                      if (selectedOption == DateFilterOption.custom && startDate != null && endDate != null) {
                        setState(() {
                          _selectedDateFilter = DateFilterOption.custom;
                        });
                        _onDateRangeChanged(startDate, endDate);
                      } else {
                        _onDateFilterChanged(selectedOption, 2);
                      }
                    },
                    backgroundColor: Colors.purple.shade50,
                    textColor: Colors.purple.shade800,
                    iconColor: Colors.purple.shade700,
                    borderColor: Colors.purple.shade200,
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.purple.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.payment,
                      color: Colors.purple.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Payments',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.purple.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    CurrencyService.formatCurrency(totalAmount),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.purple.shade900,
                      fontSize: 22,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Payment breakdown
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Bill Payments',
                  value: CurrencyService.formatCurrency(billPayments),
                  subtitle:
                      '${((billPayments / totalAmount) * 100).toStringAsFixed(1)}% of total',
                  icon: Icons.receipt,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Credit',
                  value: CurrencyService.formatCurrency(creditPayments),
                  subtitle:
                      '${((creditPayments / totalAmount) * 100).toStringAsFixed(1)}% of total',
                  icon: Icons.savings,
                  iconColor: Colors.indigo.shade700,
                  bgColor: Colors.indigo.shade50,
                  borderColor: Colors.indigo.shade200,
                  textColor: Colors.indigo.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentBillsCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Bills',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade900,
              fontSize: 18,
            ),
          ),

          const SizedBox(height: 16),

          // Recent bills list
          if (_recentBills.isNotEmpty) ...[
            ...List.generate(_recentBills.length, (index) {
              final bill = _recentBills[index];
              final customer = _customersMap[bill.customerId];
              return _buildRecentItemTile(
                title: customer?.name ?? 'Unknown Customer',
                subtitle: DateFormat('MMM d, yyyy').format(bill.billDate),
                amount: bill.amount,
                icon: Icons.receipt_long,
                iconColor: bill.isPaid ? Colors.green : Colors.red,
                status: bill.isPaid
                    ? 'Paid'
                    : (bill.isPartiallyPaid ? 'Partial' : 'Unpaid'),
              );
            }),
          ] else ...[
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No recent bills',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecentPaymentsCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Payments',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.purple.shade900,
              fontSize: 18,
            ),
          ),

          const SizedBox(height: 16),

          // Recent payments list
          if (_recentPayments.isNotEmpty) ...[
            ...List.generate(_recentPayments.length, (index) {
              final payment = _recentPayments[index];
              final customer = _customersMap[payment.customerId];
              return _buildRecentItemTile(
                title: customer?.name ?? 'Unknown Customer',
                subtitle: DateFormat('MMM d, yyyy').format(payment.paymentDate),
                amount: payment.amount,
                icon: Icons.payment,
                iconColor: Colors.green,
                status: payment.billId > 0 ? 'Bill Payment' : 'Credit',
              );
            }),
          ] else ...[
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No recent payments',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecentExpensesCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Expenses',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.indigo.shade900,
              fontSize: 18,
            ),
          ),

          const SizedBox(height: 16),

          // Recent expenses list
          if (_recentExpenses.isNotEmpty) ...[
            ...List.generate(_recentExpenses.length, (index) {
              final expense = _recentExpenses[index];
              return _buildRecentItemTile(
                title: expense.category.isEmpty
                    ? 'Uncategorized'
                    : expense.category,
                subtitle: DateFormat('MMM d, yyyy').format(expense.date),
                amount: expense.amount,
                icon: Icons.account_balance_wallet,
                iconColor: Colors.red,
                status: expense.paymentMethod ?? 'Unknown',
                isExpense: true,
              );
            }),
          ] else ...[
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No recent expenses',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _onDateRangeChanged(DateTime? startDate, DateTime? endDate) {
    setState(() {
      _startDate = startDate;
      _endDate = endDate;
      _selectedDateFilter = DateFilterOption.custom; // Ensure we update the filter option
    });

    // Refresh data with new date range
    _refreshData();
  }

  void _onDateFilterChanged(DateFilterOption option, int tabIndex) {
    // Apply the date filter
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    DateTime? startDate;
    DateTime? endDate;

    switch (option) {
      case DateFilterOption.today:
        startDate = today;
        endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last7Days:
        startDate = today.subtract(const Duration(days: 6));
        endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.thisMonth:
        startDate = DateTime(today.year, today.month, 1);
        // Last day of current month
        final nextMonth = today.month < 12
            ? DateTime(today.year, today.month + 1, 1)
            : DateTime(today.year + 1, 1, 1);
        endDate = nextMonth.subtract(const Duration(days: 1));
        endDate = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
        break;
      case DateFilterOption.thisYear:
        startDate = DateTime(today.year, 1, 1);
        endDate = DateTime(today.year, 12, 31, 23, 59, 59);
        break;
      case DateFilterOption.custom:
        // Keep current dates for custom option
        return;
    }

    setState(() {
      _selectedDateFilter = option;
      _startDate = startDate;
      _endDate = endDate;
    });

    // Refresh data with new date range
    _refreshData();
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'unpaid':
        return Colors.red;
      case 'partial':
        return Colors.blue;
      case 'credit':
        return Colors.purple;
      case 'bill payment':
        return Colors.indigo;
      default:
        return Colors.grey.shade700;
    }
  }

  // Chart building methods
  Widget _buildBillsBarChart() {
    // Ensure we have valid summary values
    final double totalAmount = _billsSummary['totalAmount']?.toDouble() ?? 0.0;
    final double paidAmount = _billsSummary['paidAmount']?.toDouble() ?? 0.0;
    final double unpaidAmount = _billsSummary['unpaidAmount']?.toDouble() ?? 0.0;
    final int paidCount = _billsSummary['paidCount']?.toInt() ?? 0;
    final int unpaidCount = _billsSummary['unpaidCount']?.toInt() ?? 0;
    final int totalCount = _billsSummary['totalCount']?.toInt() ?? 0;

    // Calculate max value for y-axis
    final double maxValue = totalAmount * 1.2;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bills Summary',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade900,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.center,
                maxY: maxValue,
                minY: 0,
                groupsSpace: 12,
                barTouchData: BarTouchData(
                  touchTooltipData: BarTouchTooltipData(
                    tooltipBgColor: Colors.white,
                    tooltipPadding: const EdgeInsets.all(8),
                    tooltipMargin: 8,
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      String label;
                      Color textColor;
                      if (groupIndex == 0) {
                        label = 'Total Bills: ${CurrencyService.formatCurrency(totalAmount)}';
                        textColor = Colors.blue.shade700;
                      } else if (groupIndex == 1) {
                        label = 'Paid Bills: ${CurrencyService.formatCurrency(paidAmount)}';
                        textColor = Colors.green.shade700;
                      } else {
                        label = 'Unpaid Bills: ${CurrencyService.formatCurrency(unpaidAmount)}';
                        textColor = Colors.red.shade700;
                      }
                      return BarTooltipItem(
                        label,
                        TextStyle(
                          color: textColor,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                  touchCallback: (FlTouchEvent event, barTouchResponse) {},
                ),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        String text = '';
                        if (value == 0) {
                          text = 'Total';
                        } else if (value == 1) {
                          text = 'Paid';
                        } else if (value == 2) {
                          text = 'Unpaid';
                        }
                        return Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            text,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        if (value == 0) {
                          return const SizedBox.shrink();
                        }
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Text(
                            value.toStringAsFixed(0),
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      },
                      reservedSize: 40,
                    ),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.shade300,
                      strokeWidth: 1,
                      dashArray: [5, 5],
                    );
                  },
                ),
                borderData: FlBorderData(
                  show: false,
                ),
                barGroups: [
                  BarChartGroupData(
                    x: 0,
                    barRods: [
                      BarChartRodData(
                        toY: totalAmount,
                        color: Colors.blue.shade400,
                        width: 40,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(6),
                          topRight: Radius.circular(6),
                        ),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 1,
                    barRods: [
                      BarChartRodData(
                        toY: paidAmount,
                        color: Colors.green.shade400,
                        width: 40,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(6),
                          topRight: Radius.circular(6),
                        ),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 2,
                    barRods: [
                      BarChartRodData(
                        toY: unpaidAmount,
                        color: Colors.red.shade400,
                        width: 40,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(6),
                          topRight: Radius.circular(6),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Bill counts indicator
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Colors.blue.shade700,
                ),
                const SizedBox(width: 8),
                Text(
                  'Total: $totalCount bills (Paid: $paidCount, Unpaid: $unpaidCount)',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillsPieChart() {
    // Ensure we have valid summary values
    final int paidCount = _billsSummary['paidCount']?.toInt() ?? 0;
    final int unpaidCount = _billsSummary['unpaidCount']?.toInt() ?? 0;
    final double paidAmount = _billsSummary['paidAmount']?.toDouble() ?? 0.0;
    final double unpaidAmount =
        _billsSummary['unpaidAmount']?.toDouble() ?? 0.0;

    // Skip if no data
    if (paidCount + unpaidCount == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bills Status',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade900,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children: [
              // Pie chart
              SizedBox(
                height: 220,
                child: PieChart(
                  PieChartData(
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                    sections: [
                      PieChartSectionData(
                        value: paidCount.toDouble(),
                        title: '$paidCount',
                        color: Colors.green.shade400,
                        radius: 80,
                        titleStyle: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      PieChartSectionData(
                        value: unpaidCount.toDouble(),
                        title: '$unpaidCount',
                        color: Colors.red.shade400,
                        radius: 80,
                        titleStyle: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Legend
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: _buildChartLegendItem(
                        color: Colors.green.shade400,
                        title: 'Paid Bills',
                        value: '$paidCount bills',
                        subtitle: CurrencyService.formatCurrency(paidAmount),
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: _buildChartLegendItem(
                        color: Colors.red.shade400,
                        title: 'Unpaid Bills',
                        value: '$unpaidCount bills',
                        subtitle: CurrencyService.formatCurrency(unpaidAmount),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentsPieChart() {
    // Use the payment methods data that was already loaded in _loadExpenseSummary
    // Skip if no data
    if (_paymentMethods.isEmpty) {
      return const SizedBox.shrink();
    }

    // Calculate total amount
    double totalAmount = 0.0;
    for (var amount in _paymentMethods.values) {
      totalAmount += amount;
    }

    if (totalAmount == 0) {
      return const SizedBox.shrink();
    }

    // Sort payment methods by amount
    List<MapEntry<String, double>> sortedMethods = _paymentMethods.entries
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // Limit to top 5 methods if there are more
    if (sortedMethods.length > 5) {
      sortedMethods = sortedMethods.sublist(0, 5);
    }

    // Define colors for payment methods
    final List<Color> methodColors = [
      Colors.purple.shade400,
      Colors.indigo.shade400,
      Colors.blue.shade400,
      Colors.deepPurple.shade400,
      Colors.teal.shade400,
    ];

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Methods',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.purple.shade900,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children: [
              // Pie chart
              SizedBox(
                height: 220,
                child: PieChart(
                  PieChartData(
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                    sections: List.generate(
                      sortedMethods.length,
                      (index) {
                        final method = sortedMethods[index];
                        final percentage = (method.value / totalAmount) * 100;
                        return PieChartSectionData(
                          value: method.value,
                          title: '${percentage.toStringAsFixed(0)}%',
                          color: methodColors[index % methodColors.length],
                          radius: 80,
                          titleStyle: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              // Legend
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Wrap(
                  alignment: WrapAlignment.center,
                  spacing: 16.0,
                  runSpacing: 16.0,
                  children: List.generate(
                    sortedMethods.length,
                    (index) {
                      final method = sortedMethods[index];
                      final percentage = (method.value / totalAmount) * 100;
                      return SizedBox(
                        width: sortedMethods.length <= 2 ? 150 : 120,
                        child: _buildChartLegendItem(
                          color: methodColors[index % methodColors.length],
                          title: method.key.isEmpty ? 'Unknown' : method.key,
                          value: CurrencyService.formatCurrency(method.value),
                          subtitle: '${percentage.toStringAsFixed(1)}%',
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExpensesPieChart() {
    // Get top categories for pie chart
    final double totalAmount = _expenseSummary['totalAmount'] ?? 0.0;
    List<MapEntry<String, double>> topCategories = [];

    if (_expenseCategories.isNotEmpty) {
      topCategories = _expenseCategories.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      // Limit to top 5 categories
      if (topCategories.length > 5) {
        topCategories = topCategories.sublist(0, 5);
      }
    }

    // Skip if no data
    if (topCategories.isEmpty || totalAmount == 0) {
      return const SizedBox.shrink();
    }

    // Define colors for categories
    final List<Color> categoryColors = [
      Colors.blue.shade400,
      Colors.indigo.shade400,
      Colors.teal.shade400,
      Colors.cyan.shade400,
      Colors.deepPurple.shade400,
    ];

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Expense Categories',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.indigo.shade900,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children: [
              // Pie chart
              SizedBox(
                height: 220,
                child: PieChart(
                  PieChartData(
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                    sections: List.generate(
                      topCategories.length,
                      (index) {
                        final category = topCategories[index];
                        final percentage = (category.value / totalAmount) * 100;
                        return PieChartSectionData(
                          value: category.value,
                          title: '${percentage.toStringAsFixed(0)}%',
                          color: categoryColors[index % categoryColors.length],
                          radius: 80,
                          titleStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              // Legend
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Wrap(
                  alignment: WrapAlignment.center,
                  spacing: 16.0,
                  runSpacing: 16.0,
                  children: List.generate(
                    topCategories.length,
                    (index) {
                      final category = topCategories[index];
                      final percentage = (category.value / totalAmount) * 100;
                      return SizedBox(
                        width: topCategories.length <= 2 ? 150 : 120,
                        child: _buildChartLegendItem(
                          color: categoryColors[index % categoryColors.length],
                          title: category.key.isEmpty
                              ? 'Uncategorized'
                              : category.key,
                          value: CurrencyService.formatCurrency(category.value),
                          subtitle: '${percentage.toStringAsFixed(1)}%',
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartLegendItem({
    required Color color,
    required String title,
    required String value,
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 26), // 0.1 opacity (26/255)
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
            color: color.withValues(alpha: 77)), // 0.3 opacity (77/255)
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                    color:
                        color.withValues(alpha: 204), // 0.8 opacity (204/255)
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
