import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing_app/widgets/app_drawer.dart';
import 'package:tubewell_water_billing_app/widgets/bottom_navigation.dart';
import 'package:tubewell_water_billing_app/screens/settings/currency_settings_screen.dart';
import 'package:tubewell_water_billing_app/screens/settings/backup_restore_settings_screen.dart';
import 'package:tubewell_water_billing_app/screens/settings/account_settings_screen.dart';
import 'package:tubewell_water_billing_app/screens/settings/app_info_screen.dart';
import 'package:tubewell_water_billing_app/screens/settings/message_templates_screen.dart';
import 'package:tubewell_water_billing_app/screens/settings/pdf_settings_screen.dart';
import 'package:tubewell_water_billing_app/utils/page_transitions.dart';

class SettingsScreenImpl extends StatefulWidget {
  const SettingsScreenImpl({super.key});

  @override
  State<SettingsScreenImpl> createState() => _SettingsScreenImplState();
}

class _SettingsScreenImplState extends State<SettingsScreenImpl> {
  // State variables
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Settings',
        showPdfOption: false,
      ),
      drawer: const AppDrawer(currentScreen: 'settings'),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 0, // Set to a valid index (0-4)
        selectedItemColor: const Color(0xFF2E7D32),
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        items: [
          BottomNavigationBarItem(
            icon: Icon(
              Icons.home,
              color: Colors.blue.shade700,
            ),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(
              Icons.receipt_long,
              color: Colors.pink,
            ),
            label: 'Bills',
          ),
          BottomNavigationBarItem(
            icon: Icon(
              Icons.people,
              color: Colors.purple,
            ),
            label: 'Customers',
          ),
          BottomNavigationBarItem(
            icon: Icon(
              Icons.payment,
              color: Colors.red,
            ),
            label: 'Payments',
          ),
          BottomNavigationBarItem(
            icon: Icon(
              Icons.account_balance_wallet,
              color: Colors.indigo,
            ),
            label: 'Expenses',
          ),
        ],
        onTap: (index) {
          // Navigate to the appropriate screen with smooth transition
          Navigator.of(context).pushReplacementFade(
            BottomNavigation(initialIndex: index),
            duration: const Duration(milliseconds: 300),
          );
        },
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  // Account Settings
                  _buildSettingsTile(
                    title: 'Accounts',
                    icon: Icons.account_circle,
                    color: Colors.purple,
                    onTap: () =>
                        _navigateToSettingsPage(const AccountSettingsScreen()),
                  ),

                  // App Info
                  _buildSettingsTile(
                    title: 'App Info',
                    icon: Icons.info_outline,
                    color: Colors.orange,
                    onTap: () => _navigateToSettingsPage(const AppInfoScreen()),
                  ),

                  // Backup & Restore
                  _buildSettingsTile(
                    title: 'Backup & Restore',
                    icon: Icons.backup,
                    color: Colors.green,
                    onTap: () => _navigateToSettingsPage(
                        const BackupRestoreSettingsScreen()),
                  ),

                  // Currency Settings
                  _buildSettingsTile(
                    title: 'Currency',
                    icon: Icons.currency_exchange,
                    color: Colors.blue,
                    onTap: () =>
                        _navigateToSettingsPage(const CurrencySettingsScreen()),
                  ),

                  // Message Templates
                  _buildSettingsTile(
                    title: 'Message Templates',
                    icon: Icons.message,
                    color: Colors.red,
                    onTap: () =>
                        _navigateToSettingsPage(const MessageTemplatesScreen()),
                  ),

                  // PDF Templates
                  _buildSettingsTile(
                    title: 'PDF Templates',
                    icon: Icons.picture_as_pdf,
                    color: Colors.cyan,
                    onTap: () =>
                        _navigateToSettingsPage(const PdfSettingsScreen()),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSettingsTile({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: const Color(0xFFE8F5E9), // Light blue background color
      // Color(0xFFE0F7FA), // Light blue background color
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withAlpha(26),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _navigateToSettingsPage(Widget page) async {
    final result = await Navigator.of(context).pushFade(
      page,
      duration: const Duration(milliseconds: 300),
    );

    // Refresh UI if needed
    if (result == true) {
      setState(() {});
    }
  }
}
