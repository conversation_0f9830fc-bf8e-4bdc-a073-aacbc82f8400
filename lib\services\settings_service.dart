import 'package:logger/logger.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/models/settings.dart';

/// A service for storing and retrieving application settings
class SettingsService {
  static bool _isInitialized = false;
  static final Logger _logger = Logger();

  // Initialize the settings service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Ensure database is initialized
    if (!DatabaseService.isInitialized) {
      await DatabaseService.initialize();
    }

    _isInitialized = true;
  }

  // Ensure the service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // Get a setting value with a default fallback
  static Future<String> getSetting(String key, String defaultValue,
      {String? accountId}) async {
    await _ensureInitialized();

    try {
      final db = await DatabaseService.database;

      // Build the query
      String whereClause = 'key = ?';
      List<dynamic> whereArgs = [key];

      if (accountId != null) {
        whereClause += ' AND accountId = ?';
        whereArgs.add(accountId);
      } else {
        whereClause += ' AND (accountId IS NULL OR accountId = "")';
      }

      // Query for the setting
      final List<Map<String, dynamic>> maps = await db.query(
        'settings',
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      if (maps.isEmpty) {
        return defaultValue;
      }

      return maps.first['value'] as String;
    } catch (e) {
      _logger.e('Error getting setting: $e');
      return defaultValue;
    }
  }

  // Save a setting value
  static Future<void> saveSetting(String key, String value,
      {String? accountId}) async {
    await _ensureInitialized();

    try {
      final db = await DatabaseService.database;

      // Build the query to check if setting exists
      String whereClause = 'key = ?';
      List<dynamic> whereArgs = [key];

      if (accountId != null) {
        whereClause += ' AND accountId = ?';
        whereArgs.add(accountId);
      } else {
        whereClause += ' AND (accountId IS NULL OR accountId = "")';
      }

      // Check if setting already exists
      final List<Map<String, dynamic>> maps = await db.query(
        'settings',
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      await db.transaction((txn) async {
        if (maps.isEmpty) {
          // Create new setting
          final setting = Settings(
            key: key,
            value: value,
            accountId: accountId,
          );

          await txn.insert('settings', setting.toMap());
        } else {
          // Update existing setting
          await txn.update(
            'settings',
            {'value': value},
            where: whereClause,
            whereArgs: whereArgs,
          );
        }
      });
    } catch (e) {
      _logger.e('Error saving setting: $e');
    }
  }

  // Currency settings
  static Future<String> getCurrencySymbol({String? accountId}) async {
    return await getSetting('currencySymbol', '₨', accountId: accountId);
  }

  static Future<void> saveCurrencySymbol(String symbol,
      {String? accountId}) async {
    await saveSetting('currencySymbol', symbol, accountId: accountId);
  }

  static Future<String> getCurrencyCode({String? accountId}) async {
    return await getSetting('currencyCode', 'PKR', accountId: accountId);
  }

  static Future<void> saveCurrencyCode(String code, {String? accountId}) async {
    await saveSetting('currencyCode', code, accountId: accountId);
  }

  static Future<String> getCountryCode({String? accountId}) async {
    return await getSetting('countryCode', '92', accountId: accountId);
  }

  static Future<void> saveCountryCode(String code, {String? accountId}) async {
    await saveSetting('countryCode', code, accountId: accountId);
  }

  // Default hourly rate setting
  static Future<String> getDefaultHourlyRate({String? accountId}) async {
    return await getSetting('defaultHourlyRate', '900', accountId: accountId);
  }

  static Future<void> saveDefaultHourlyRate(String rate, {String? accountId}) async {
    await saveSetting('defaultHourlyRate', rate, accountId: accountId);
  }

  // Backup settings
  static Future<String> getLastBackupDate({String? accountId}) async {
    return await getSetting('lastBackupDate', 'Never', accountId: accountId);
  }

  static Future<void> saveLastBackupDate(String date,
      {String? accountId}) async {
    await saveSetting('lastBackupDate', date, accountId: accountId);
  }

  // Clear all settings for an account
  static Future<void> clearAccountSettings(String accountId) async {
    await _ensureInitialized();

    try {
      final db = await DatabaseService.database;

      // Delete all settings for this account
      await db.transaction((txn) async {
        await txn.delete(
          'settings',
          where: 'accountId = ?',
          whereArgs: [accountId],
        );
      });

      // Settings cleared successfully
    } catch (e) {
      _logger.e('Error clearing account settings: $e');
    }
  }

  // Message template settings
  static Future<String?> getMessageTemplate({
    required String? accountId,
    required String templateType,
  }) async {
    final key = 'messageTemplate_$templateType';
    final value = await getSetting(key, '', accountId: accountId);
    return value.isEmpty ? null : value;
  }

  static Future<void> saveMessageTemplate({
    required String? accountId,
    required String templateType,
    required String template,
  }) async {
    final key = 'messageTemplate_$templateType';
    await saveSetting(key, template, accountId: accountId);
  }

  static Future<void> deleteMessageTemplate({
    required String? accountId,
    required String templateType,
  }) async {
    final key = 'messageTemplate_$templateType';
    await saveSetting(key, '', accountId: accountId);
  }
}
