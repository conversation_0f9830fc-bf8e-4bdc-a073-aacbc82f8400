# PowerShell script to fix the Flutter shared.bat bug
# This script fixes the PUSHD/git command issue in <PERSON>lut<PERSON>'s shared.bat file

$flutterSharedBat = "C:\src\flutter\bin\internal\shared.bat"

if (Test-Path $flutterSharedBat) {
    Write-Host "Found Flutter shared.bat file at: $flutterSharedBat"

    # Read the content line by line
    $lines = Get-Content $flutterSharedBat

    # Create a backup first
    $backupPath = "$flutterSharedBat.backup3"
    Copy-Item $flutterSharedBat $backupPath
    Write-Host "Created backup at: $backupPath"

    # Fix the problematic lines (69-71)
    $fixedLines = @()
    $lineNumber = 0
    $inProblemSection = $false

    foreach ($line in $lines) {
        $lineNumber++

        # Check if we're at the problematic FOR loop line
        if ($line -match "FOR /f %%r IN \('PUSHD %FLUTTER_ROOT% \^& git rev-parse HEAD'\) DO \(") {
            Write-Host "Line $lineNumber - Replacing problematic FOR loop"
            $fixedLines += "    PUSHD `"%FLUTTER_ROOT%`""
            $fixedLines += "    FOR /f %%r IN ('git rev-parse HEAD') DO ("
            $inProblemSection = $true
        }
        # Check if we're at the closing parenthesis of the FOR loop
        elseif ($inProblemSection -and $line -match "^\s*\)\s*$") {
            $fixedLines += "    )"
            $fixedLines += "    POPD"
            $inProblemSection = $false
            Write-Host "Line $lineNumber - Added POPD after FOR loop"
        }
        # Skip the SET revision line if we're in the problem section
        elseif ($inProblemSection -and $line -match "SET revision=%%r") {
            $fixedLines += $line
        }
        else {
            $fixedLines += $line
        }
    }

    # Write the fixed content
    $fixedLines | Set-Content $flutterSharedBat
    Write-Host "Fixed the Flutter shared.bat file!"

    # Verify the fix
    Write-Host "`nVerifying fix..."
    $verifyLines = Get-Content $flutterSharedBat | Select-String -Pattern "PUSHD|git rev-parse|POPD" -Context 1
    foreach ($verifyLine in $verifyLines) {
        Write-Host "Found: $verifyLine"
    }

} else {
    Write-Host "Flutter shared.bat file not found at: $flutterSharedBat"
    Write-Host "Please check your Flutter installation path."
}

Write-Host "`nYou can now try running 'flutter run' again."
