import 'package:tubewell_water_billing_app/services/currency_service.dart';

class PaymentAllocation {
  int id;
  int paymentId;
  int billId;
  double amount;
  String? remarks;
  DateTime createdAt;

  // Constructor
  PaymentAllocation({
    this.id = 0,
    required this.paymentId,
    required this.billId,
    required this.amount,
    this.remarks,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now() {
    // Set a default remark if none provided
    remarks ??= billId > 0
        ? 'Allocation of ${CurrencyService.formatCurrency(amount)} for bill #$billId'
        : 'Credit allocation of ${CurrencyService.formatCurrency(amount)}';
  }

  // Convert a PaymentAllocation object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'paymentId': paymentId,
      'billId': billId,
      'amount': amount,
      'remarks': remarks,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Create a PaymentAllocation object from a Map
  factory PaymentAllocation.fromMap(Map<String, dynamic> map) {
    return PaymentAllocation(
      id: map['id'],
      paymentId: map['paymentId'],
      billId: map['billId'],
      amount: map['amount'],
      remarks: map['remarks'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  @override
  String toString() {
    return 'PaymentAllocation{id: $id, paymentId: $paymentId, billId: $billId, amount: $amount}';
  }
}
