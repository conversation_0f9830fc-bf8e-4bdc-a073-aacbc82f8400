import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';

/// A widget that displays a summary of bills totals
class BillSummary extends StatelessWidget {
  final int totalCount;
  final double totalAmount;
  final double paidAmount;
  final double unpaidAmount;
  final int paidCount;
  final int unpaidCount;
  final bool showDetailedView;
  final VoidCallback? onToggleView;

  const BillSummary({
    super.key,
    required this.totalCount,
    required this.totalAmount,
    required this.paidAmount,
    required this.unpaidAmount,
    this.paidCount = 0,
    this.unpaidCount = 0,
    this.showDetailedView = false,
    this.onToggleView,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.teal.shade100),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.summarize, color: Colors.teal.shade700),
                const SizedBox(width: 8),
                Text(
                  'Bills Summary',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.teal.shade800,
                  ),
                ),
                const Spacer(),
                if (onToggleView != null)
                  InkWell(
                    onTap: onToggleView,
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Row(
                        children: [
                          Icon(
                            showDetailedView
                                ? Icons.view_agenda
                                : Icons.view_list,
                            size: 20,
                            color: Colors.teal.shade800,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            showDetailedView ? 'Simple' : 'Detailed',
                            style: TextStyle(
                              color: Colors.teal.shade800,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildSummaryItem(
                  context,
                  'Total Bills',
                  totalCount.toString(),
                  Icons.receipt_long,
                  const Color(0xFF5C6BC0), // Indigo
                ),
                _buildSummaryItem(
                  context,
                  'Total Amount',
                  CurrencyService.formatCurrency(totalAmount),
                  Icons.payments,
                  const Color(0xFF6A1B9A), // Deep Purple
                ),
                _buildSummaryItem(
                  context,
                  'Paid',
                  CurrencyService.formatCurrency(paidAmount),
                  Icons.check_circle,
                  const Color(0xFF2E7D32), // Green
                ),
                _buildSummaryItem(
                  context,
                  'Outstanding',
                  CurrencyService.formatCurrency(unpaidAmount),
                  Icons.money_off,
                  const Color(0xFFB71C1C), // Red
                ),
              ],
            ),
            if (showDetailedView) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildDetailCard(
                      context,
                      title: 'Paid',
                      amount: paidAmount,
                      count: paidCount,
                      icon: Icons.check_circle,
                      color: const Color(0xFF2E7D32), // Green
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildDetailCard(
                      context,
                      title: 'Unpaid',
                      amount: unpaidAmount,
                      count: unpaidCount,
                      icon: Icons.pending_actions,
                      color: const Color(0xFFB71C1C), // Red
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF455A64), // Blue Grey (instead of grey)
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailCard(
    BuildContext context, {
    required String title,
    required double amount,
    required int count,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(76)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Rs ${amount.toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const Text(
                    'Amount',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF455A64), // Blue Grey (instead of grey)
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    count.toString(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const Text(
                    'Bills',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF455A64), // Blue Grey (instead of grey)
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
