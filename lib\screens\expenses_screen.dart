import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'package:tubewell_water_billing_app/models/expense.dart';
import 'package:tubewell_water_billing_app/services/expense_service.dart';
import 'package:tubewell_water_billing_app/services/currency_service.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing_app/widgets/app_drawer.dart';
import 'package:tubewell_water_billing_app/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing_app/forms/expense_form_screen.dart';
import 'package:tubewell_water_billing_app/widgets/universal_fab.dart';
import 'package:tubewell_water_billing_app/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing_app/utils/navigation_helper.dart';

import 'package:tubewell_water_billing_app/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_settings.dart';

class ExpensesScreen extends StatefulWidget {
  const ExpensesScreen({super.key});

  @override
  State<ExpensesScreen> createState() => _ExpensesScreenState();
}

class _ExpensesScreenState extends State<ExpensesScreen> {
  // Constants
  static const int _pageSize = 20;

  // Pagination
  int _currentPage = 0;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // State variables
  List<Expense> _expenses = [];
  bool _isLoading = true;
  String _searchQuery = '';

  // Filters
  final TextEditingController _searchController = TextEditingController();
  String? _selectedCategory;
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedPaymentMethod;

  // Debounce search to reduce unnecessary rebuilds
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // Summary data
  Map<String, double> _expenseSummary = {
    'totalAmount': 0.0,
    'count': 0.0,
  };
  Map<String, double> _categoryTotals = {};

  // Scroll controller for pagination
  final ScrollController _scrollController = ScrollController();

  // Selection mode
  bool _isSelectionMode = false;
  final Set<int> _selectedExpenseIds = <int>{};

  // Category icons map
  final Map<String, IconData> _categoryIcons = {
    'Electricity': Icons.electric_bolt,
    'Maintenance': Icons.handyman,
    'Repairs': Icons.build,
    'Fuel': Icons.local_gas_station,
    'Salaries': Icons.payments,
    'Equipment': Icons.construction,
    'Rent': Icons.home,
    'Taxes': Icons.receipt_long,
    'Transportation': Icons.directions_car,
    'Office Supplies': Icons.business_center,
    'Other': Icons.category,
  };

  // Category colors - ensuring no orange, yellow, grey, and no duplicates
  final Map<String, Color> _categoryColors = {
    'Electricity': const Color(0xFF1E88E5), // Blue
    'Maintenance': const Color(0xFF43A047), // Green
    'Repairs': const Color(0xFF5E35B1), // Deep Purple
    'Fuel': const Color(0xFFD81B60), // Pink
    'Salaries': const Color(0xFF00897B), // Teal
    'Equipment': const Color(0xFF8E24AA), // Purple
    'Rent': const Color(0xFF3949AB), // Indigo
    'Taxes': const Color(0xFF00ACC1), // Cyan
    'Transportation': const Color(0xFF7CB342), // Light Green
    'Office Supplies': const Color(0xFF0D47A1), // Dark Blue
    'Other': const Color(0xFF4A148C), // Dark Purple
  };

  // Payment method icons map
  final Map<String, IconData> _paymentMethodIcons = {
    'Cash': Icons.money,
    'Bank Transfer': Icons.account_balance,
    'Check': Icons.receipt_long,
    'Credit Card': Icons.credit_card,
    'Other': Icons.payment,
  };

  // Payment method colors
  final Map<String, Color> _paymentMethodColors = {
    'Cash': const Color(0xFF43A047), // Green
    'Bank Transfer': const Color(0xFF3949AB), // Indigo
    'Check': const Color(0xFF5E35B1), // Deep Purple
    'Credit Card': const Color(0xFFD81B60), // Pink
    'Other': const Color(0xFF00897B), // Teal
  };



  // Stream subscription for data changes
  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(_onSearchChanged);
    _loadExpenses();

    // Listen for data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.expense ||
          changeType == DataChangeType.all) {
        if (mounted) {
          _refreshExpenses();
        }
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _dataChangeSubscription.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      final query = _searchController.text.trim().toLowerCase();
      if (_searchQuery != query) {
        setState(() {
          _searchQuery = query;
        });
        _loadExpenses(resetPagination: true);
      }
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >
        _scrollController.position.maxScrollExtent - 200) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMoreExpenses();
      }
    }
  }

  Future<void> _loadExpenses({bool resetPagination = true}) async {
    if (resetPagination) {
      setState(() {
        _isLoading = true;
        _currentPage = 0;
      });
    }

    try {
      // Load summary data
      final summary = await ExpenseService.getExpenseSummary(
        startDate: _startDate,
        endDate: _endDate,
        category: _selectedCategory,
      );

      final categoryTotals = await ExpenseService.getExpensesByCategory(
        startDate: _startDate,
        endDate: _endDate,
      );

      // Load expense list
      final expenses = await ExpenseService.getAllExpenses(
        offset: _currentPage * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        category: _selectedCategory,
        startDate: _startDate,
        endDate: _endDate,
        paymentMethod: _selectedPaymentMethod,
      );

      if (mounted) {
        setState(() {
          _expenseSummary = summary;
          _categoryTotals = categoryTotals;

          if (resetPagination) {
            _expenses = expenses;
          } else {
            _expenses.addAll(expenses);
          }

          _isLoading = false;
          _hasMoreData = expenses.length >= _pageSize;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading expenses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadMoreExpenses() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;

      final expenses = await ExpenseService.getAllExpenses(
        offset: _currentPage * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        category: _selectedCategory,
        startDate: _startDate,
        endDate: _endDate,
        paymentMethod: _selectedPaymentMethod,
      );

      if (mounted) {
        setState(() {
          _expenses.addAll(expenses);
          _hasMoreData = expenses.length >= _pageSize;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading more expenses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshExpenses() async {
    return _loadExpenses(resetPagination: true);
  }

  // Check if any filters are active
  bool get _isFilteringActive =>
      _selectedCategory != null ||
      _startDate != null ||
      _endDate != null ||
      _selectedPaymentMethod != null;

  void _applyFilters({
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    String? paymentMethod,
  }) {
    setState(() {
      _selectedCategory = category;
      _startDate = startDate;
      _endDate = endDate;
      _selectedPaymentMethod = paymentMethod;
    });
    _loadExpenses(resetPagination: true);
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
      _selectedCategory = null;
      _startDate = null;
      _endDate = null;
      _selectedPaymentMethod = null;
    });
    _loadExpenses(resetPagination: true);
  }

  Future<void> _deleteExpense(Expense expense) async {
    try {
      final deleted = await ExpenseService.deleteExpense(expense.id);

      if (mounted) {
        if (deleted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Expense deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          _refreshExpenses();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete expense'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting expense: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmation(Expense expense) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Expense'),
        content: Text('Are you sure you want to delete this expense?\n\n'
            'Amount: ${CurrencyService.formatCurrency(expense.amount)}\n'
            'Category: ${expense.category}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteExpense(expense);
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToExpenseForm({Expense? existingExpense}) async {
    await NavigationHelper.navigateTo(
      context,
      ExpenseFormScreen(
        existingExpense: existingExpense,
      ),
    );

    if (mounted) {
      _refreshExpenses();
    }
  }

  Future<void> _generateExpensesPdf() async {
    if (!mounted) return;

    try {
      // Get all expenses that match the current filters
      final allFilteredExpenses = await ExpenseService.getAllExpenses(
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        category: _selectedCategory,
        startDate: _startDate,
        endDate: _endDate,
        paymentMethod: _selectedPaymentMethod,
        // Don't use pagination for PDF generation
        offset: 0,
        limit: 1000, // Use a large limit to get all expenses
      );

      if (!mounted) return;

      // Create PDF data
      final Map<String, dynamic> pdfData = {
        'expenses': allFilteredExpenses,
        'summary': _expenseSummary,
        'categoryTotals': _categoryTotals,
        'filters': {
          'category': _selectedCategory,
          'startDate': _startDate,
          'endDate': _endDate,
          'paymentMethod': _selectedPaymentMethod,
          'searchQuery': _searchQuery.isNotEmpty ? _searchQuery : null,
        },
      };

      // Use the universal PDF service to generate the PDF
      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': 'Expense Report',
          },
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showFilterDialog() {
    // Store temporary filters that can be reverted if canceled
    String? tempCategory = _selectedCategory;
    DateTime? tempStartDate = _startDate;
    DateTime? tempEndDate = _endDate;
    String? tempPaymentMethod = _selectedPaymentMethod;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Filter Expenses'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date range filter
                  const Text(
                    'Date Range',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final picked = await showDatePicker(
                              context: context,
                              initialDate: tempStartDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (picked != null) {
                              setState(() {
                                tempStartDate = picked;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              tempStartDate != null
                                  ? DateFormat('MMM d, yyyy')
                                      .format(tempStartDate!)
                                  : 'Start Date',
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final picked = await showDatePicker(
                              context: context,
                              initialDate: tempEndDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (picked != null) {
                              setState(() {
                                tempEndDate = picked;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              tempEndDate != null
                                  ? DateFormat('MMM d, yyyy')
                                      .format(tempEndDate!)
                                  : 'End Date',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Category filter
                  const Text(
                    'Category',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String?>(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: tempCategory,
                    hint: const Text('Select Category'),
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Row(
                          children: [
                            Icon(
                              Icons.all_inclusive,
                              color: Colors.blue,
                              size: 20,
                            ),
                            SizedBox(width: 12),
                            Text('All Categories'),
                          ],
                        ),
                      ),
                      ...ExpenseService.getPredefinedCategories().map((category) {
                        return DropdownMenuItem<String?>(
                          value: category,
                          child: Row(
                            children: [
                              Icon(
                                _categoryIcons[category] ?? Icons.category,
                                color: _categoryColors[category] ?? Colors.blue,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Text(category),
                            ],
                          ),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        tempCategory = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // Payment method filter
                  const Text(
                    'Payment Method',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String?>(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: tempPaymentMethod,
                    hint: const Text('Select Payment Method'),
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Row(
                          children: [
                            Icon(
                              Icons.all_inclusive,
                              color: Colors.blue,
                              size: 20,
                            ),
                            SizedBox(width: 12),
                            Text('All Payment Methods'),
                          ],
                        ),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'Cash',
                        child: Row(
                          children: [
                            Icon(
                              _paymentMethodIcons['Cash'] ?? Icons.money,
                              color: _paymentMethodColors['Cash'] ?? Colors.green,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            const Text('Cash'),
                          ],
                        ),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'Bank Transfer',
                        child: Row(
                          children: [
                            Icon(
                              _paymentMethodIcons['Bank Transfer'] ?? Icons.account_balance,
                              color: _paymentMethodColors['Bank Transfer'] ?? Colors.indigo,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            const Text('Bank Transfer'),
                          ],
                        ),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'Credit Card',
                        child: Row(
                          children: [
                            Icon(
                              _paymentMethodIcons['Credit Card'] ?? Icons.credit_card,
                              color: _paymentMethodColors['Credit Card'] ?? Colors.pink,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            const Text('Credit Card'),
                          ],
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        tempPaymentMethod = value;
                      });
                    },
                  ),
                ],
              ),
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Wrap(
                  alignment: WrapAlignment.spaceEvenly,
                  spacing: 8.0,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _clearFilters();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Clear'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _applyFilters(
                          category: tempCategory,
                          startDate: tempStartDate,
                          endDate: tempEndDate,
                          paymentMethod: tempPaymentMethod,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7D32),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Apply'),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildExpenseItem(Expense expense) {
    final categoryIcon = _categoryIcons[expense.category] ?? Icons.category;
    final formattedDate = DateFormat('dd MMM yyyy').format(expense.date);
    final formattedAmount = CurrencyService.formatCurrency(expense.amount);
    final isSelected = _selectedExpenseIds.contains(expense.id);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected ? Colors.amber.shade500 : Colors.indigo.shade400,
            width: isSelected ? 2.0 : 1.5,
          ),
        ),
        color: isSelected ? Colors.amber.shade50 : null,
        child: InkWell(
          onTap: _isSelectionMode
              ? () {
                  setState(() {
                    if (_selectedExpenseIds.contains(expense.id)) {
                      _selectedExpenseIds.remove(expense.id);
                      if (_selectedExpenseIds.isEmpty) {
                        _isSelectionMode = false;
                      }
                    } else {
                      _selectedExpenseIds.add(expense.id);
                    }
                  });
                  HapticFeedback.selectionClick();
                }
              : () => _navigateToExpenseForm(existingExpense: expense),
          onLongPress: _isSelectionMode
              ? null
              : () {
                  setState(() {
                    _isSelectionMode = true;
                    _selectedExpenseIds.add(expense.id);
                  });
                  HapticFeedback.heavyImpact();
                },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Category and Payment Method
                Row(
                  children: [
                    // Selection checkbox when in selection mode
                    if (_isSelectionMode) ...[
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) {
                          setState(() {
                            if (isSelected) {
                              _selectedExpenseIds.remove(expense.id);
                              if (_selectedExpenseIds.isEmpty) {
                                _isSelectionMode = false;
                              }
                            } else {
                              _selectedExpenseIds.add(expense.id);
                            }
                          });
                          HapticFeedback.selectionClick();
                        },
                        activeColor: Colors.amber.shade700,
                        checkColor: Colors.white,
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Category with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.teal.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.teal.shade200),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              categoryIcon,
                              color: Colors.teal.shade700,
                              size: 14,
                            ),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                expense.category,
                                style: TextStyle(
                                  color: Colors.teal.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Payment Method with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(_getPaymentMethodIcon(expense.paymentMethod),
                                size: 14, color: Colors.blue.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                expense.paymentMethod ?? 'Cash',
                                style: TextStyle(
                                  color: Colors.blue.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button in row 1 (only when not in selection mode)
                    if (!_isSelectionMode)
                      GestureDetector(
                        onTap: () =>
                            _navigateToExpenseForm(existingExpense: expense),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.cyan.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.cyan.shade200),
                          ),
                          child: Icon(Icons.edit,
                              color: Colors.cyan.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Row 2: Date and Amount
                Row(
                  children: [
                    // Date with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 14, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Amount with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.payments,
                                size: 14, color: Colors.red.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedAmount,
                                style: TextStyle(
                                  color: Colors.red.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    if (!_isSelectionMode)
                      GestureDetector(
                        onTap: () => _showDeleteConfirmation(expense),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Icon(Icons.delete,
                              color: Colors.red.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                // Row 3: Remarks (if available)
                if (expense.remarks != null && expense.remarks!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade50,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: Colors.amber.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.comment,
                            size: 14, color: Colors.amber.shade700),
                        const SizedBox(width: 3),
                        Expanded(
                          child: Text(
                            expense.remarks!,
                            style: TextStyle(
                              color: Colors.amber.shade800,
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExpensesList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 80), // Space for FAB
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _expenses.length + (_hasMoreData ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _expenses.length) {
          return _isLoadingMore
              ? const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              : const SizedBox.shrink();
        }

        final expense = _expenses[index];
        return _buildExpenseItem(expense);
      },
    );
  }

  Widget _buildExpenseSummaryCard() {
    // Get top categories
    List<MapEntry<String, double>> topCategories = [];
    if (_categoryTotals.isNotEmpty) {
      topCategories = _categoryTotals.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      // Limit to top 3 categories
      if (topCategories.length > 3) {
        topCategories = topCategories.sublist(0, 3);
      }
    }

    // Ensure we have valid summary values
    final int expenseCount = _expenseSummary['count']?.toInt() ?? 0;
    final double totalAmount =
        _expenseSummary['totalAmount']?.toDouble() ?? 0.0;

    // Calculate average expense
    final double averageAmount =
        expenseCount > 0 ? totalAmount / expenseCount : 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.indigo.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.shade200),
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Expenses Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo.shade900,
                  fontSize: 18,
                ),
              ),
              Text(
                '$expenseCount expenses',
                style: TextStyle(
                  color: Colors.indigo.shade800,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.indigo.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.indigo.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.money_off,
                      color: Colors.indigo.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Expenses',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.indigo.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _isLoading
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          CurrencyService.formatCurrency(totalAmount),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                            color: Colors.indigo.shade700,
                          ),
                        ),
                      ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Detailed stats
          Row(
            children: [
              // Average expense
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Average',
                  value: CurrencyService.formatCurrency(averageAmount),
                  icon: Icons.calculate,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
              const SizedBox(width: 16),
              // Top category if available
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: topCategories.isNotEmpty
                      ? 'Top Category'
                      : 'No Categories',
                  value:
                      topCategories.isNotEmpty ? topCategories.first.key : '-',
                  icon: topCategories.isNotEmpty
                      ? _categoryIcons[topCategories.first.key] ??
                          Icons.category
                      : Icons.category,
                  iconColor: Colors.teal.shade700,
                  bgColor: Colors.teal.shade50,
                  borderColor: Colors.teal.shade200,
                  textColor: Colors.teal.shade800,
                ),
              ),
            ],
          ),

          // Date range indicator if filters are applied
          if (_startDate != null || _endDate != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.date_range,
                    color: Colors.purple.shade700,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      _startDate != null && _endDate != null
                          ? '${DateFormat('MMM d, yyyy').format(_startDate!)} - ${DateFormat('MMM d, yyyy').format(_endDate!)}'
                          : _startDate != null
                              ? 'From ${DateFormat('MMM d, yyyy').format(_startDate!)}'
                              : 'Until ${DateFormat('MMM d, yyyy').format(_endDate!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.purple.shade800,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Helper method to build a financial summary item
  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color borderColor,
    required Color textColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: textColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: textColor,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get payment method icon
  IconData _getPaymentMethodIcon(String? method) {
    if (method == null) return Icons.money;

    switch (method.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'bank transfer':
        return Icons.account_balance;
      case 'check':
        return Icons.receipt_long;
      case 'credit card':
        return Icons.credit_card;
      default:
        return Icons.payment;
    }
  }

  void _deleteSelectedExpenses() async {
    if (_selectedExpenseIds.isEmpty) return;

    final count = _selectedExpenseIds.length;

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Expenses'),
        content:
            Text('Are you sure you want to delete $count selected expenses?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    try {
      int successCount = 0;

      // Delete each selected expense
      for (final id in _selectedExpenseIds) {
        final success = await ExpenseService.deleteExpense(id);
        if (success) successCount++;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deleted $successCount of $count expenses'),
            backgroundColor: successCount > 0 ? Colors.green : Colors.orange,
          ),
        );

        // Exit selection mode and refresh
        setState(() {
          _isSelectionMode = false;
          _selectedExpenseIds.clear();
        });

        _refreshExpenses();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting expenses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    if (_isLoading && _expenses.isEmpty) {
      // Show loading indicator when initially loading
      content = const Center(child: CircularProgressIndicator());
    } else if (_expenses.isEmpty) {
      // Show empty state when no expenses are found
      content = EmptyStateWidget(
        icon: _searchQuery.isNotEmpty || _isFilteringActive
            ? Icons.search_off
            : Icons.receipt_long,
        title: _searchQuery.isNotEmpty || _isFilteringActive
            ? 'No Results Found'
            : 'No Expenses Yet',
        message: _searchQuery.isNotEmpty || _isFilteringActive
            ? 'Try different search terms or filters'
            : 'Add your first expense to get started.',
        buttonText: _searchQuery.isNotEmpty || _isFilteringActive
            ? 'Clear Filters'
            : 'Add Expense',
        onButtonPressed: () {
          if (_searchQuery.isNotEmpty || _isFilteringActive) {
            _clearFilters();
          } else {
            _navigateToExpenseForm();
          }
        },
      );
    } else {
      // Show expense list
      content = _buildExpensesList();
    }

    return Scaffold(
      appBar: _isSelectionMode
          ? AppBar(
              backgroundColor: const Color(0xFF2E7D32),
              title: Text('${_selectedExpenseIds.length} selected'),
              leading: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = false;
                    _selectedExpenseIds.clear();
                  });
                },
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: _selectedExpenseIds.isEmpty
                      ? null
                      : () => _deleteSelectedExpenses(),
                ),
              ],
            )
          : TubewellAppBar(
              title: 'Expenses',
              currentScreen: 'expenses',
              showBackButton: false,
              onPdfPressed: _generateExpensesPdf,
              pdfData: {
                'expenses': _expenses,
                'summary': _expenseSummary,
                'categoryTotals': _categoryTotals,
                'filters': {
                  'category': _selectedCategory,
                  'startDate': _startDate,
                  'endDate': _endDate,
                  'searchQuery': _searchQuery.isNotEmpty ? _searchQuery : null,
                },
              },
            ),
      drawer: const AppDrawer(currentScreen: 'expenses'),
      body: Column(
        children: [
          // Summary card - always show
          _buildExpenseSummaryCard(),

          // Search and filter bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search expenses...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                  },
                                )
                              : null,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: Colors.indigo.shade300),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: _isFilteringActive
                            ? const Color(0xFF2E7D32)
                            : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.filter_list,
                          color: _isFilteringActive
                              ? Colors.white
                              : Colors.grey.shade700,
                        ),
                        onPressed: _showFilterDialog,
                        tooltip: 'Filter',
                      ),
                    ),
                  ],
                ),

                // Active filters indicator
                if (_searchQuery.isNotEmpty || _isFilteringActive) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        'Found ${_expenses.length} results',
                        style: TextStyle(
                          color: Colors.indigo.shade600,
                          fontStyle: FontStyle.italic,
                          fontSize: 13,
                        ),
                      ),
                      const Spacer(),
                      if (_isFilteringActive)
                        Chip(
                          label: const Text('Filters Applied'),
                          deleteIcon: const Icon(Icons.close, size: 18),
                          onDeleted: _clearFilters,
                          backgroundColor: const Color(0xFFE8F5E9),
                          deleteIconColor: const Color(0xFF2E7D32),
                          labelStyle: const TextStyle(
                              color: Color(0xFF2E7D32), fontSize: 12),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 0),
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                        ),
                      if (_searchQuery.isNotEmpty)
                        TextButton(
                          onPressed: () {
                            _searchController.clear();
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: const Color(0xFF2E7D32),
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            minimumSize: const Size(0, 32),
                          ),
                          child: const Text('Clear Search'),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Main content
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshExpenses,
              color: const Color(0xFF2E7D32),
              child: content,
            ),
          ),
        ],
      ),
      floatingActionButton: _isSelectionMode
          ? null
          : UniversalFab(
              type: FabType.expense,
              heroTag: 'fab-expenses',
              onResult: (result) {
                if (result == true) {
                  _refreshExpenses();
                }
              },
            ),
    );
  }
}
