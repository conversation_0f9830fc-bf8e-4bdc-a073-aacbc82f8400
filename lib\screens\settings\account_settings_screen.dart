import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing_app/services/account_service.dart';
import 'package:tubewell_water_billing_app/models/account.dart';
import 'package:tubewell_water_billing_app/widgets/bottom_navigation.dart';
import 'package:tubewell_water_billing_app/utils/page_transitions.dart';

class AccountSettingsScreen extends StatefulWidget {
  const AccountSettingsScreen({super.key});

  @override
  State<AccountSettingsScreen> createState() => _AccountSettingsScreenState();
}

class _AccountSettingsScreenState extends State<AccountSettingsScreen> {
  // Account settings
  Account? _currentAccount;
  List<Account> _availableAccounts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAccountSettings();
  }

  Future<void> _loadAccountSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize services if needed
      await AccountService.initialize();

      // Get current account
      final currentAccount = AccountService.currentAccount;
      final accounts = AccountService.accounts;

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _currentAccount = currentAccount;
        _availableAccounts = List.from(accounts);
        _isLoading = false;
      });
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading account settings: $e'),
          backgroundColor: Colors.red,
        ),
      );

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addAccount() async {
    try {
      final result = await showDialog<Map<String, String?>>(
        context: context,
        builder: (context) {
          final nameController = TextEditingController();
          final descriptionController = TextEditingController();
          return AlertDialog(
            title: const Text('Add New Account'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Account Name',
                    hintText: 'Enter account name',
                    helperText: 'Required',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (Optional)',
                    hintText: 'Enter account description',
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  // Validate input before closing dialog
                  if (nameController.text.trim().isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Account name cannot be empty')),
                    );
                    return;
                  }
                  Navigator.of(context).pop({
                    'name': nameController.text.trim(),
                    'description': descriptionController.text.trim(),
                  });
                },
                child: const Text('Add'),
              ),
            ],
          );
        },
      );

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      if (result == null || result['name'] == null || result['name']!.isEmpty) {
        return;
      }

      final newAccountName = result['name']!;
      final description = result['description'];

      // Check if account with this name already exists
      if (_availableAccounts.any((account) => account.name == newAccountName)) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Account with this name already exists')),
        );
        return;
      }

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Creating account...'),
            ],
          ),
        ),
      );

      try {
        // Add the account using the service
        final newAccount = await AccountService.addAccount(newAccountName,
            description: description);

        // Check if widget is still mounted before continuing
        if (!mounted) return;

        // Close loading dialog
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }

        setState(() {
          _availableAccounts.add(newAccount);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Account "$newAccountName" added')),
        );
      } catch (e) {
        // Check if widget is still mounted before continuing
        if (!mounted) return;

        // Close loading dialog
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding account: $e')),
        );
      }
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Unexpected error: $e')),
      );
    }
  }

  Future<void> _switchAccount(Account account) async {
    try {
      // Show a loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Switching account...'),
            ],
          ),
        ),
      );

      // Switch the account using the service
      await AccountService.switchAccount(account.id);

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // Reload settings after switching
      await _loadAccountSettings();

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // Close the loading dialog
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Switched to "${account.name}"')),
      );

      // Navigate to BottomNavigation with the settings tab selected (index 3) using smooth transition
      // This ensures the bottom navigation bar is visible after switching accounts
      Navigator.of(context).pushReplacementFade(
        const BottomNavigation(initialIndex: 3),
        duration: const Duration(milliseconds: 400),
      );
    } catch (e) {
      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // Close the loading dialog if open
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error switching account: $e')),
      );
    }
  }

  Future<void> _editAccount(Account account) async {
    final nameController = TextEditingController(text: account.name);
    final descriptionController =
        TextEditingController(text: account.description ?? '');

    final result = await showDialog<Map<String, String?>>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Edit Account'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Account Name',
                  hintText: 'Enter account name',
                  helperText: 'Required',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  hintText: 'Enter account description',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Validate input before closing dialog
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Account name cannot be empty')),
                  );
                  return;
                }

                // Check if name is already taken by another account
                final newName = nameController.text.trim();
                if (newName != account.name &&
                    _availableAccounts
                        .any((a) => a.id != account.id && a.name == newName)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Account with this name already exists')),
                  );
                  return;
                }

                Navigator.of(context).pop({
                  'name': newName,
                  'description': descriptionController.text.trim(),
                });
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );

    // Check if widget is still mounted before continuing
    if (!mounted) return;

    if (result == null) return;

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Updating account...'),
            ],
          ),
        ),
      );

      // Update the account
      final updatedAccount = Account(
        id: account.id,
        name: result['name']!,
        description: result['description'],
        createdAt: account.createdAt,
        lastAccessed: account.lastAccessed,
      );

      await AccountService.updateAccount(updatedAccount);

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // Close loading dialog
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      // Refresh the account list
      await _loadAccountSettings();

      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Account "${updatedAccount.name}" updated')),
      );
    } catch (e) {
      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // Close loading dialog if open
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating account: $e')),
      );
    }
  }

  Future<void> _deleteAccount(Account account) async {
    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text(
            'Are you sure you want to delete the account "${account.name}"? '
            'This will permanently delete all data associated with this account.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    // Check if widget is still mounted before continuing
    if (!mounted) return;

    if (confirmed != true) return;

    try {
      // Delete the account using the service
      await AccountService.deleteAccount(account.id);

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _availableAccounts.removeWhere((a) => a.id == account.id);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Account "${account.name}" deleted')),
      );
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting account: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Account Settings',
        showPdfOption: false,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                _buildCurrentAccount(),
                const SizedBox(height: 20),
                _buildAccountsList(),
                const SizedBox(height: 20),
                _buildAccountInfo(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addAccount,
        backgroundColor: Colors.green,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCurrentAccount() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Current Account',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  const Icon(Icons.account_circle,
                      color: Colors.blue, size: 36),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Active Account',
                            style: TextStyle(color: Colors.grey, fontSize: 12)),
                        Text(
                          _currentAccount?.name ?? 'Default Account',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                        if (_currentAccount?.description != null &&
                            _currentAccount!.description!.isNotEmpty)
                          Text(_currentAccount!.description!,
                              style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountsList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Available Accounts',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addAccount,
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add New'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_availableAccounts.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                alignment: Alignment.center,
                child: const Text(
                    'No accounts available. Add one to get started.'),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _availableAccounts.length,
                itemBuilder: (context, index) {
                  final account = _availableAccounts[index];
                  final isCurrentAccount = _currentAccount?.id == account.id;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    color: isCurrentAccount ? Colors.blue.shade50 : null,
                    elevation: isCurrentAccount ? 2 : 1,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color: isCurrentAccount
                            ? Colors.blue.shade300
                            : Colors.grey.shade300,
                        width: isCurrentAccount ? 1.5 : 0.5,
                      ),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      title: Text(
                        account.name,
                        style: TextStyle(
                          fontWeight: isCurrentAccount
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                      subtitle: account.description != null &&
                              account.description!.isNotEmpty
                          ? Text(account.description!)
                          : null,
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isCurrentAccount
                              ? Colors.green.shade100
                              : Colors.grey.shade100,
                        ),
                        child: Center(
                          child: Icon(
                            isCurrentAccount
                                ? Icons.check_circle
                                : Icons.account_circle,
                            color:
                                isCurrentAccount ? Colors.green : Colors.grey,
                            size: 24,
                          ),
                        ),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Edit button (available for all accounts)
                          IconButton(
                            icon: const Icon(Icons.edit,
                                color: Colors.blue, size: 20),
                            onPressed: () => _editAccount(account),
                            tooltip: 'Edit Account',
                          ),

                          // Delete button (only for non-current accounts)
                          if (!isCurrentAccount)
                            IconButton(
                              icon: const Icon(Icons.delete,
                                  color: Colors.red, size: 20),
                              onPressed: () => _deleteAccount(account),
                              tooltip: 'Delete Account',
                            ),

                          // Switch button (only for non-current accounts)
                          if (!isCurrentAccount)
                            OutlinedButton(
                              onPressed: () => _switchAccount(account),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.blue,
                                side: BorderSide(color: Colors.blue.shade300),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                              ),
                              child: const Text('Switch'),
                            )
                          else
                            const Chip(
                              label: Text('Active'),
                              backgroundColor: Colors.green,
                              labelStyle:
                                  TextStyle(color: Colors.white, fontSize: 12),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 0),
                            ),
                        ],
                      ),
                      onTap: isCurrentAccount
                          ? null
                          : () => _switchAccount(account),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Account Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.amber, size: 16),
                      SizedBox(width: 8),
                      Text('Account Data Isolation',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Each account has its own separate database. When you switch accounts, you will only see data for the current account.',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
